import { formatCode } from './../codeformat';
import { GeneratorConfig } from '../../model/generator/generatorConfig';
import { camelCase, cloneDeep, upperFirst } from 'lodash-es';
import { ColumnType } from '/@/model/generator/tableInfo';
import {
  ComponentConfigModel,
  ComponentOptionModel,
  FrontCodeModel,
} from '/@/model/generator/codeGenerator';
import { QueryConfig } from '/@/model/generator/listConfig';
import { FormProps } from '/@/components/Form';
import { TableInfo } from '/@/components/Designer';
import { camelCaseString } from '/@/utils/event/design';
import { handleAppSearchForm, handleSearchForm } from './exportHelper';
import { PrintButton } from '/@/enums/printEnum';
import { AppFormProps } from '/@/model/generator/appFormConfig';
import { GeneratorAppModel } from '/@/api/system/generator/model';
import { buildAppComponentType, setApiConfig } from './designHelper';
import { getWorkflowPermissionConfig } from '/@/hooks/web/useWorkFlowForm';

/**
 * 构建代码
 * @param model 配置
 * @param tableInfo 后端数据库表信息
 */
export function buildCode(
  model: GeneratorConfig,
  tableInfo: TableInfo[],
  formProps: FormProps,
): FrontCodeModel {
  const frontCode: FrontCodeModel = {
    listCode: buildListCode(model),
    formCode: buildFormCode(model, tableInfo),
    apiCode: buildApiCode(model, tableInfo),
    modelCode: buildModelCode(model, tableInfo),
    configJsonCode: buildConfigJsonCode(model, formProps),
    workflowPermissionCode: buildWorkflowPermissionConfigJsonCode(formProps), //工作流权限配置
    simpleFormCode: buildSimpleFormCode(model, tableInfo), //simpleForm页面
  };

  return frontCode;
}

/**
 * 构建代码
 * @param model 配置
 * @param tableInfo 后端数据库表信息
 */
export function buildAppCode(
  model: GeneratorConfig,
  formProps: AppFormProps,
  designType: string,
): GeneratorAppModel {
  const frontCode: GeneratorAppModel = {
    className: model.outputConfig.className,
    outputArea: model.outputConfig.outputArea,
    outputValue: model.outputConfig.outputValue,
    listCode: buildAppListCode(model),
    formCode: buildAppFormCode(model),
    containerCode: buildAppContainerCode(),
    apiCode: buildAppApiCode(model),
    tagString: buildTagStringCode(model),
    configJsonCode: buildAppConfigJsonCode(model, formProps, designType),
  };

  return frontCode;
}

/**
 * 构建api代码
 * @param model 配置
 */
export function buildApiCode(model: GeneratorConfig, _tableInfo: TableInfo[]): string {
  const className = model.outputConfig.className;
  // const lowerClassName = lowerCase(className);
  const lowerClassName = className?.toLowerCase();
  const pascalClassName = upperFirst(camelCaseString(className!, false));

  let mainTable;
  if (model.tableConfigs && model.tableConfigs.length) {
    //数据优先
    mainTable = model.tableConfigs?.find((item) => item.isMain);
  } else {
    //界面优先、简易模板
    mainTable = model.tableStructureConfigs?.find((item) => item.isMain);
  }

  if (!mainTable) {
    throw new Error('请设置主表');
  }
  const hasSubformList = model.formJson.list.some((item) => {
    return item.type === 'form' && item.options!.isListView;
  });
  const mainTableName = mainTable?.tableName;

  //将表名转为驼峰命名 首字母小写驼峰
  const camelCaseMainTableName = camelCase(mainTableName);
  //将表名转为帕斯卡命名 首字母大写的驼峰
  const pascalMainTableName = upperFirst(camelCase(camelCaseMainTableName));
  const hasSetUserIdButton = hasButton(model.listConfig.buttonConfigs, 'batchSetUserId');
  const hasExportButton = hasButton(model.listConfig.buttonConfigs, 'export');

  const code = `
import { ${pascalMainTableName}PageModel, ${pascalMainTableName}PageParams, ${pascalMainTableName}PageResult } from './model/${pascalClassName}Model';
import { defHttp } from '/@/utils/http/axios';
import { ErrorMessageMode } from '/#/axios';

enum Api {
  Page = '/${model.outputConfig.outputValue}/${lowerClassName}/page',
  List = '/${model.outputConfig.outputValue}/${lowerClassName}/list',
  Info = '/${model.outputConfig.outputValue}/${lowerClassName}/info',
  Add = '/${model.outputConfig.outputValue}/${lowerClassName}/add',
  Update = '/${model.outputConfig.outputValue}/${lowerClassName}/update',
  Delete = '/${model.outputConfig.outputValue}/${lowerClassName}/delete',

  ${
    hasSetUserIdButton
      ? `
  DataAuth = '/${model.outputConfig.outputValue}/${lowerClassName}/data-auth',`
      : ''
  }
  ${
    hasExportButton
      ? `
  Export = '/${model.outputConfig.outputValue}/${lowerClassName}/export',`
      : ''
  }
  ${
    hasSubformList
      ? `
  ListChildren = '/${model.outputConfig.outputValue}/${lowerClassName}/list-children'`
      : ''
  }
}

/**
 * @description: 查询${pascalMainTableName}分页列表
 */
export async function get${pascalMainTableName}Page(params: ${pascalMainTableName}PageParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<${pascalMainTableName}PageResult>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取${pascalMainTableName}信息
 */
export async function get${pascalMainTableName}(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<${pascalMainTableName}PageModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增${pascalMainTableName}
 */
export async function add${pascalMainTableName}(${camelCaseMainTableName}: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Add,
      params: ${camelCaseMainTableName},
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新${pascalMainTableName}
 */
export async function update${pascalMainTableName}(${camelCaseMainTableName}: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Update,
      params: ${camelCaseMainTableName},
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除${pascalMainTableName}（批量删除）
 */
export async function delete${pascalMainTableName}(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Delete,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

${
  hasSetUserIdButton
    ? `
/**
 * @description: 修改权限${pascalMainTableName}
 */
export async function setDataAuth${pascalMainTableName}(${camelCaseMainTableName}: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.DataAuth,
      params: ${camelCaseMainTableName},
    },
    {
      errorMessageMode: mode,
    },
  );
}
  `
    : ''
}
${
  hasExportButton
    ? `
/**
 * @description: 导出${pascalMainTableName}
 */
export async function export${pascalMainTableName}(
  params?: object, 
  mode: ErrorMessageMode = 'modal'
) {
  return defHttp.download(
    {
      url: Api.Export,
      method: 'GET',
      params,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}
  `
    : ''
}
${
  hasSubformList
    ? `
/**
 * @description: 查询子表数据
 */
export async function getChildren${pascalMainTableName}(params: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.ListChildren,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}`
    : ''
}
`;
  return code;
}

/**
 * 构建类型代码
 * @param model 配置
 * @param tableInfo 后端数据库表信息
 */
export function buildModelCode(model: GeneratorConfig, tableInfo: TableInfo[]): string {
  let mainTable;
  let childTables;
  if (model.tableConfigs && model.tableConfigs.length) {
    //数据优先
    mainTable = model.tableConfigs?.find((item) => item.isMain);
    childTables = model.tableConfigs?.filter((item) => !item.isMain);
  } else {
    //界面优先、简易模板
    mainTable = model.tableStructureConfigs?.find((item) => item.isMain);
    childTables = model.tableStructureConfigs?.filter((item) => !item.isMain);
    mainTable.pkField = 'id';
  }

  if (!mainTable) {
    throw new Error('请设置主表');
  }
  const mainTableName = mainTable?.tableName;

  //将表名转为驼峰命名 首字母小写驼峰
  const camelCaseMainTableName = camelCase(mainTableName);
  //将表名转为帕斯卡命名 首字母大写的驼峰
  const pascalMainTableName = upperFirst(camelCaseMainTableName);

  const code = `
  import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

${
  model.listConfig.queryConfigs.length > 0
    ? `
/**
* @description: ${pascalMainTableName}分页参数 模型
*/
export interface ${pascalMainTableName}PageParams extends BasicPageParams {
 ${model.listConfig.queryConfigs
   .map((item) => {
     const table = tableInfo?.find((x) => x.isMain); //找到主表信息
     const field = table?.fields.find((x) => x.name === item.fieldName); //找到当前字段信息

     if (field?.type === ColumnType.DATE) {
       return `
 ${camelCaseString(item.fieldName) + 'Start'}: string;
 ${camelCaseString(item.fieldName) + 'End'}: string;`;
     } else {
       return `
 ${camelCaseString(item.fieldName)}: string;`;
     }
   })
   .join('\n')}
}
`
    : `
    /**
    * @description: ${pascalMainTableName}分页参数 模型
    */
   export type ${pascalMainTableName}PageParams = BasicPageParams;
   `
}


/**
 * @description: ${pascalMainTableName}分页返回值模型
 */
export interface ${pascalMainTableName}PageModel {
  ${camelCaseString(mainTable.pkField) + ': string; \n'}
  ${model.listConfig.columnConfigs
    .map((item) => {
      if (item.columnName !== mainTable.pkField) {
        return `
  ${camelCaseString(item.columnName)}: string;`;
      }
    })
    .join('\n')}
}

${
  Array.isArray(tableInfo) &&
  tableInfo.length &&
  model.tableConfigs
    ?.map((item) => {
      const tableNameFirstUpperCase = upperFirst(camelCase(item.tableName));
      //当前遍历的表信息
      const thisTableInfo = tableInfo?.find((table) => table.name === item.tableName);
      const thisTableColumnInfo = thisTableInfo?.fields;
      // 如果是主表 默认需要加入子表的List字段
      if (item.isMain) {
        return `
/**
 * @description: ${tableNameFirstUpperCase}表类型
 */
export interface ${tableNameFirstUpperCase}Model {
  ${thisTableColumnInfo
    ?.map((column) => {
      return `
  ${camelCaseString(column.name)}: ${column.type === ColumnType.NUMBER ? 'number' : 'string'};`;
    })
    .join('\n')}

  ${
    childTables &&
    childTables
      .map((childTable) => {
        const childTablePascalName = upperFirst(camelCaseString(childTable.tableName));
        return `
  ${camelCaseString(childTable.tableName) + 'List?'} : ${childTablePascalName}Model;`;
      })
      .join('\n')
  }
} 
    `;
      } else {
        return `
/**
 * @description: ${tableNameFirstUpperCase}表类型
 */
export interface ${tableNameFirstUpperCase}Model {
  ${thisTableColumnInfo
    ?.map((column) => {
      return `  
  ${camelCaseString(column.name)}: ${column.type === ColumnType.NUMBER ? 'number' : 'string'};`;
    })
    .join('\n')}
} 
    `;
      }
    })
    .join('\n')
}



/**
 * @description: ${pascalMainTableName}分页返回值结构
 */
export type ${pascalMainTableName}PageResult = BasicFetchResult<${pascalMainTableName}PageModel>;
\n
  `;
  return formatCode(code);
}

/**
 * 构建列表页代码
 * @param model 配置
 */
export function buildListCode(model: GeneratorConfig): string {
  console.log('buildListCode', model);
  const className = model.outputConfig.className;
  const formType = model.formJson.config.formType;
  // const lowerClassName = lowerCase(className);
  const lowerClassName = className?.toLowerCase();
  const pascalClassName = upperFirst(camelCaseString(className!, false));

  // //是否有左侧菜单
  // const isMenu = model.listConfig.isLeftMenu;
  // //左侧占比
  // const treeFlex = model.listConfig.leftMenuConfig?.leftWidth;
  let mainTable;
  if (model.tableConfigs && model.tableConfigs.length) {
    //数据优先
    mainTable = model.tableConfigs?.find((item) => item.isMain);
  } else {
    //界面优先、简易模板
    mainTable = model.tableStructureConfigs?.find((item) => item.isMain);
    mainTable.pkField = 'id';
  }

  if (!mainTable) {
    throw new Error('请设置主表');
  }

  const mainTableName = mainTable?.tableName;

  //将表名转为驼峰命名 首字母小写驼峰
  const camelCaseMainTableName = camelCase(mainTableName);
  //将表名转为帕斯卡命名 首字母大写的驼峰
  const pascalMainTableName = upperFirst(camelCase(camelCaseMainTableName));
  const componentArray = [] as string[];
  model.listConfig.columnConfigs.map((config) => {
    componentArray.push(config.componentType!);
  });
  let btnEvent = `{`;
  model.listConfig.buttonConfigs
    .filter((x) => x.isUse && x.isDefault)
    .forEach((x) => {
      btnEvent += `${x.code} : handle${upperFirst(x.code)},`;
    });

  btnEvent += '}';
  const hasSubformList = model.formJson.list.some((item) => {
    return item.type === 'form' && item.options!.isListView;
  });

  const hasFilterButton = model.listConfig.columnConfigs.some((item) => item.isFilter);

  const buttonConfigs = model.listConfig.buttonConfigs;
  buttonConfigs.map((item, index) => {
    if (item.code === 'delete') {
      //删除按钮放在最后
      buttonConfigs.splice(index, 1);
      buttonConfigs.push(item);
    }
    // if (item.code === 'pushorder') {
    //   //推单按钮放在最后
    //   buttonConfigs.splice(index, 1);
    //   buttonConfigs.unshift(item);
    // }
  });
  const hasAddButton = hasButton(buttonConfigs, 'add');
  const hasEditButton = hasButton(buttonConfigs, 'edit');
  const hasViewButton = hasButton(buttonConfigs, 'view');
  const hasBatchDeleteButton = hasButton(buttonConfigs, 'batchdelete');
  const hasDeleteButton = hasButton(buttonConfigs, 'delete');
  const hasImportButton = hasButton(buttonConfigs, 'import');
  const hasExportButton = hasButton(buttonConfigs, 'export');
  const hasSetUserIdButton = hasButton(buttonConfigs, 'batchSetUserId');
  const hasStartWorkButton = hasButton(buttonConfigs, 'startwork');
  const hasFlowRecordButton = hasButton(buttonConfigs, 'flowRecord');
  const hasPrintButton = hasButton(buttonConfigs, 'print');
  const hasCopyDataButton = hasButton(buttonConfigs, 'copyData');
  const hasPushOrderButton = hasButton(buttonConfigs, 'pushorder');
  const hasTemplatePrint = hasButton(buttonConfigs, PrintButton.CODE);
  const isSetDataAuth = model.outputConfig.isDataAuth;
  const isCardList = model.listConfig.listStyle === 'card';
  const hasRowCheck =
    hasBatchDeleteButton || hasTemplatePrint || (hasSetUserIdButton && isSetDataAuth);

  //判断是否有用到数据源数据字典的组件
  const code = `
<template>
<ResizePageWrapper ${model.listConfig.isLeftMenu ? ':hasLeft="true"' : ':hasLeft="false"'}>
 ${
   model.listConfig.isLeftMenu
     ? `<template #resizeLeft>
 ${
   model?.listConfig?.leftMenuConfig?.treeConfig.id
     ? `
        <TreeStructure :treeConfig="treeConfig" @select="handleSelectTree"/>
        `
     : `
        <BasicTree
          title="${model.listConfig.leftMenuConfig?.menuName || ``}"
          toolbar
          search
          :clickRowToExpand="true"
          :treeData="treeData"
          :fieldNames="{ key: '${
            model.listConfig.leftMenuConfig?.datasourceType === 'static' ? `key` : `value`
          }', title: '${
         model.listConfig.leftMenuConfig?.datasourceType === 'dic'
           ? `name`
           : model.listConfig.leftMenuConfig?.datasourceType === 'api'
           ? `label`
           : model.listConfig.leftMenuConfig?.showFieldName || `title`
       }' }"
          @select="handleSelect"
        >
          <template #title="item">
          ${
            model.listConfig.leftMenuConfig?.parentIcon
              ? `<template v-if="item.renderIcon === 'parentIcon'">
                  <Icon icon="${model.listConfig.leftMenuConfig?.parentIcon}" />
                </template>`
              : ''
          }
          ${
            model.listConfig.leftMenuConfig?.childIcon
              ? `<template v-if="item.renderIcon === 'childIcon'">
                  <Icon icon="${model.listConfig.leftMenuConfig?.childIcon}" />
                </template>`
              : ''
          }
            &nbsp;&nbsp;{{ ${
              model.listConfig.leftMenuConfig?.datasourceType === 'dic'
                ? `item.name`
                : model.listConfig.leftMenuConfig?.datasourceType === 'api'
                ? `item.label`
                : `item.${model.listConfig.leftMenuConfig?.showFieldName || 'title'}`
            } }}
          </template>
        </BasicTree>
        `
 }
 </template>`
     : ``
 }
 
 <template #resizeRight>
<BasicTable @register="registerTable" isMenuTable ref="tableRef" ${
    hasRowCheck && !isCardList
      ? `:row-selection="{ selectedRowKeys: selectedKeys, onChange: onSelectChange }"`
      : ''
  }${hasSubformList ? ' @expand="expandedRowsChange"' : ''}>
       ${
         hasFilterButton
           ? `<template #customFilterIcon="{ filtered, column }" >
          <Icon icon="ant-design:filter-filled"  @click="handleFilterClick(column)" :class="filterClass(column,filtered)" :style="{ color: filtered ? '#108ee9' : undefined }"/>
        </template>`
           : ''
       }
        <template #toolbar>
          <template v-for="button in tableButtonConfig" :key="button.code">
            <a-button v-if="button.isDefault" type="primary" v-auth="\`${lowerClassName}:\${button.code}\`" @click="buttonClick(button.code)">
              <template #icon><Icon :icon="button.icon" /></template>
              {{ button.name }}
            </a-button>
            <a-button v-else type="primary">
              <template #icon><Icon :icon="button.icon" /></template>
              {{ button.name }}
            </a-button>
          </template>
        </template>
        ${
          isCardList
            ? `
        <template #emptyText>
        ${
          hasRowCheck
            ? `<a-checkbox-group v-model:value="selectedKeys" class="data-list" v-if="datasource.length">`
            : `<div class="data-list" v-if="datasource.length">`
        }
              <div
                v-for="(data, idx) in datasource"
                :key="data.id"
                ${
                  hasRowCheck
                    ? `
                :class="[
                  'box-container',
                  selectedKeys.includes(data.id) ? 'selected-box' : 'unselected-box',
                ]"`
                    : `
                class="box-container unselected-box"`
                }
              >
                <div class="box-title">
                  <div>
        ${hasRowCheck ? `<a-checkbox :value="data.id" class="!mr-1" />` : ''}
                    <span class="text-black">#{{ idx + 1 }}</span>
                  </div>
                  <div>
                    <a-tooltip v-for="(item, index) in getActions(data)" :key="index">
                      <template #title>{{ item.tooltip }} </template>
                      <Icon
                        :icon="item.icon"
                        v-auth="item.auth"
                        :class="['icon-box', item.color === 'error' ? 'icon-delete' : '']"
                        @click="item.onClick()"
                      />
                    </a-tooltip>
                    ${
                      hasPushOrderButton
                        ? `
                    <div class="btn-box" ref="pushBtnRef" v-if="isShowBtn && pushFormId === data.id">
                      <a-button
                        type="primary"
                        v-for="info in pushorderInfo!.setting.formInfo"
                        :key="info.formId"
                        @click="openPushorder(info)"
                      >
                        {{ info.formType === 0 ? info.functionName : info.formName }}
                      </a-button>
                    </div> `
                        : ''
                    }
                  </div>
                </div>
                <div class="flex-box">
                  <div 
                    v-for="(key, value, index) in getData(data)" 
                    :key="index" 
                    :class="\`flex-item text-\${ key.align || 'left' } \${ key.aRow ? '!basis-full' : '' }\`"
                  >
                    <div :class="['title', key.textBold ? 'font-bold' : '']">{{ value }}</div>
                    <div class="value">{{ key.value }}</div>
                  </div>
                </div>
              </div>
        ${hasRowCheck ? `</a-checkbox-group>` : `</div>`}
          <div v-else>
            <a-empty />
          </div>
        </template>`
            : `<template #bodyCell="{ column, record }">
          ${
            componentArray.includes('switch')
              ? `<template v-if="column.componentType === 'switch'">
                  <a-switch
                    v-model:checked="record[column.dataIndex]"
                    :unCheckedValue="0"
                    :checkedValue="1"
                    :disabled="true"
                  />
                </template>`
              : ''
          }
          ${
            componentArray.includes('picker-color')
              ? `<template v-if="column.componentType === 'picker-color'">
                  <ColorPicker
                    v-model:value="record[column.dataIndex]"
                    :disabled="true"
                  />
                </template>`
              : ''
          }
         ${
           componentArray.includes('labelComponent')
             ? `<template v-if="column.componentType === 'labelComponent'">
                  <XjrLabelComponentInfo
                   :value="record[column.dataIndex]"
                  :styleConfig="column.styleConfig"
                  />
                </template>`
             : ''
         }
          <template v-if="column.dataIndex === 'action'">
          ${
            hasPushOrderButton
              ? `
            <div class="btn-box" ref="pushBtnRef" v-if="isShowBtn && pushFormId === record.id">
              <a-button
                type="primary"
                v-for="item in pushorderInfo!.setting.formInfo"
                :key="item.formId"
                @click="openPushorder(item)"
              >
                {{ item.formType === 0 ? item.functionName : item.formName }}
              </a-button>
            </div>`
              : ''
          }
            <TableAction :actions="getActions(record)" />
          </template>
          ${
            componentArray.includes('money-chinese')
              ? ` <template v-else-if="column.componentType === 'money-chinese'">
                    <span :style="executeListStyle(record, column?.listStyle)">
                      {{ moneyChineseData(record[column.dataIndex]) }}
                    </span>
                  </template>`
              : ''
          }
          <template v-else-if="column.staticOptions?.length">
            <span :style="executeListStyle(record, column?.listStyle)">
              {{
                column.staticOptions.filter((x) => x.value === record[column.dataIndex])[0]?.label
              }}
            </span>
          </template>
          <template v-else-if="column.dataIndex && column?.listStyle">
            <span :style="executeListStyle(record, column?.listStyle)">{{
              record[column.dataIndex]
            }}</span>
          </template>
        </template>
        ${
          hasSubformList
            ? `
        <template #expandedRowRender="{ record }">
          <a-table
            v-for="item in subFormList"
            :key="item.key"
            :columns="innerColumns[item.field]"
            :data-source="getInnerDataSource(record.id, item.field)"
            :pagination="false"
            :scroll="{ x: 'max-content' }"
          >
            <template #bodyCell="{ column, record: innerRecord }">
              <template v-if="column.componentType === 'picker-color'">
                <ColorPicker v-model:value="innerRecord[column.dataIndex]" :disabled="true" />
              </template>
                <template v-else-if="column.componentType === 'labelComponent'">
                  <XjrLabelComponentInfo
                    :value="innerRecord[column.dataIndex]"
                    :styleConfig="column.styleConfig"
                  />
                </template>
              <template v-else-if="column.componentType === 'money-chinese'">
                {{ moneyChineseData(innerRecord[column.dataIndex]) }}
              </template>
            </template>
          </a-table>
        </template>`
            : ''
        }
           `
        }
        
      </BasicTable>
 </template>
 
  ${
    hasStartWorkButton
      ? `
      <LookProcess
        v-if="visibleLookProcessRef"
        :taskId="taskIdRef"
        :processId="processIdRef"
        @close="visibleLookProcessRef = false"
        :visible="visibleLookProcessRef"
      />
      <LaunchProcess
        v-if="visibleLaunchProcessRef"
        :schemaId="schemaIdRef"
        :form-data="formDataRef"
        :form-id="formIdComputedRef"
        :rowKeyData="rowKeyData"
        :draftsId="draftsId"
        @close="handleCloseLaunch"
      />
      <ApprovalProcess
        v-if="visibleApproveProcessRef"
        :taskId="taskIdRef"
        :processId="processIdRef"
        :schemaId="schemaIdRef"
        @close="handleCloseApproval"
        :visible="visibleApproveProcessRef"
      />
      ${
        hasFlowRecordButton
          ? `<BasicModal
      v-if="visibleFlowRecordModal"
      :visible="visibleFlowRecordModal"
      :title="t('查看流转记录')"
      :paddingRight="15"
      :showOkBtn="false"
      :width="1200"
      @visible-change="
        (v) => {
          visibleFlowRecordModal = v;
        }
      "
      :bodyStyle="{ minHeight: '400px !important' }"
    >
      <FlowRecord :processId="processIdRef" />
    </BasicModal>`
          : ''
      }`
      : ''
  }
  ${
    hasTemplatePrint
      ? ` <PrintPreview
    v-if="printData.visible"
    :id="printData.id"
    :request-params-configs="printData.requestParamsConfigs"
    :request-body-configs="printData.requestBodyConfigs"
    :request-header-configs="printData.requestHeaderConfigs"
    @close="printData.visible = false"
  />`
      : ''
  }
  ${
    formType === 'modal'
      ? ` <${pascalClassName}Modal @register="registerModal" @success="handleSuccess" />`
      : ` <${pascalClassName}Drawer @register="registerDrawer" @success="handleSuccess" />`
  }
  ${
    hasPushOrderButton
      ? `
    <PushOrderModal @register="registerPushModal" />`
      : ''
  }
  ${
    hasImportButton
      ? ` <ImportModal @register="registerImportModal" importUrl="/${model.outputConfig.outputValue}/${lowerClassName}/import" @success="handleImportSuccess"/>`
      : ''
  }
   ${
     hasExportButton
       ? `<ExportModal
      v-if="visibleExport"
      @close="visibleExport = false"
      :columns="columns"
      @success="handleExportSuccess"
    />`
       : ''
   }
  ${
    hasSetUserIdButton && isSetDataAuth
      ? ` <SetRuleUserModal @register="registerRuleUserModal" @success="handleSuccess" />`
      : ''
  }
</ResizePageWrapper>

</template>
<script lang="ts" setup>
  import { ref, computed,${model.listConfig.isLeftMenu || hasSubformList ? 'onMounted,' : ''}${
    hasBatchDeleteButton || hasDeleteButton || (hasSetUserIdButton && !isSetDataAuth)
      ? 'createVNode,'
      : ''
  }${
    model.listConfig.isLeftMenu && model.listConfig.leftMenuConfig?.datasourceType === 'api'
      ? 'getCurrentInstance,'
      : ''
  } 
${hasTemplatePrint ? ' reactive, ' : ''}${hasPushOrderButton ? 'nextTick,' : ''}
} from 'vue';
  ${
    hasDeleteButton ||
    hasBatchDeleteButton ||
    (hasSetUserIdButton && !isSetDataAuth) ||
    hasPushOrderButton
      ? `
  import { Modal } from 'ant-design-vue';
  import { ExclamationCircleOutlined${
    hasSetUserIdButton ? ', CloseCircleOutlined' : ''
  } } from '@ant-design/icons-vue';`
      : ''
  }
  import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table';
  import { get${pascalMainTableName}Page, delete${pascalMainTableName}${
    hasSetUserIdButton && isSetDataAuth ? `, setDataAuth${pascalMainTableName}` : ''
  }${hasExportButton ? `, export${pascalMainTableName}` : ''}${
    hasSubformList ? `, getChildren${pascalMainTableName}` : ''
  }${hasPushOrderButton ? `, get${pascalMainTableName}` : ''}} from '/@/api/${
    model.outputConfig.outputValue
  }/${lowerClassName}';
  import { ResizePageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { executeListStyle } from '/@/hooks/web/useListStyle';//列表样式配置
  import { useRouter } from 'vue-router';
  
  ${
    hasPrintButton
      ? `
  import printJS from 'print-js';`
      : ''
  }
  ${
    hasTemplatePrint
      ? `
		import { getPrintConfigInfo } from '/@/api/system/generator/print';
		import PrintPreview from '/@/views/generator/print/Preview.vue'; 
	  import { PrintButton } from '/@/enums/printEnum';
	  import { InputParamItem } from '/@/components/ApiConfig/src/interface';
    `
      : ''
  }
  ${
    formType === 'modal'
      ? `
  import { useModal${hasFlowRecordButton ? `,BasicModal` : ''} } from '/@/components/Modal';`
      : `
  import { useDrawer } from '/@/components/Drawer';`
  }
  ${
    (hasImportButton || (hasSetUserIdButton && isSetDataAuth)) && formType !== 'modal'
      ? `import { useModal${hasFlowRecordButton ? `,BasicModal` : ''} } from '/@/components/Modal';`
      : ''
  }
  ${
    hasStartWorkButton
      ? `
  import LookProcess from '/@/views/workflow/task/components/LookProcess.vue';
  import LaunchProcess from '/@/views/workflow/task/components/LaunchProcess.vue';
  import ApprovalProcess from '/@/views/workflow/task/components/ApprovalProcess.vue';
  import { getDraftInfo } from '/@/api/workflow/process';`
      : ''
  }
  ${
    formType === 'modal'
      ? `
  import ${pascalClassName}Modal from './components/${pascalClassName}Modal.vue';
  `
      : `
  import ${pascalClassName}Drawer from './components/${pascalClassName}Drawer.vue';//
  `
  }
  ${
    hasPushOrderButton
      ? `
  import PushOrderModal from '/@/views/form/template/components/PushOrderModal.vue';`
      : ''
  }
  ${
    hasSetUserIdButton && isSetDataAuth
      ? `
  import SetRuleUserModal from '/@/components/Designer/src/components/generateComponent/SetRuleUserModal.vue';`
      : ''
  }
  ${hasImportButton ? `import { ImportModal } from '/@/components/Import';` : ''}
  ${
    hasExportButton
      ? `
  import { downloadByData } from '/@/utils/file/download';
  import ExportModal from '/@/views/form/template/components/ExportModal.vue';
    `
      : ''
  }
  ${
    componentArray.includes('picker-color') || hasSubformList
      ? `import { ColorPicker } from '/@/components/ColorPicker';`
      : ''
  }
  ${
    componentArray.includes('labelComponent') || hasSubformList
      ? `  import { XjrLabelComponentInfo } from '/@/components/LabelComponent';`
      : ''
  }
  import { searchFormSchema, columns${
    hasSubformList ? ', formProps' : ''
  } } from './components/config';
  ${
    model.listConfig.leftMenuConfig?.treeConfig?.id
      ? `import { treeConfig } from './components/config';  
         import {TreeStructure} from '/@/components/TreeStructure';`
      : ``
  }
  ${
    model.listConfig.buttonConfigs.filter((x) => x.isUse).length > 0 ||
    model.listConfig.leftMenuConfig?.childIcon ||
    model.listConfig.leftMenuConfig?.parentIcon
      ? `import Icon from '/@/components/Icon/index';`
      : ``
  }
  ${
    model.listConfig.isLeftMenu && model.listConfig.leftMenuConfig?.datasourceType === 'datasource'
      ? `import { listToTree } from '/@/utils/helper/treeHelper';`
      : ``
  } 
  ${model.listConfig.isLeftMenu ? `import { BasicTree, TreeItem } from '/@/components/Tree';` : ``} 
  ${
    model.listConfig.isLeftMenu && model.listConfig.leftMenuConfig?.datasourceType === 'dic'
      ? `import { getDicDetailList } from '/@/api/system/dic';`
      : ``
  }
  ${
    model.listConfig.isLeftMenu && model.listConfig.leftMenuConfig?.datasourceType === 'datasource'
      ? `import { getDatasourceData } from '/@/api/system/datasource';`
      : ``
  }
  ${
    (model.listConfig.isLeftMenu && model.listConfig.leftMenuConfig?.datasourceType === 'api') ||
    hasPrintButton ||
    hasStartWorkButton ||
    hasPushOrderButton ||
    componentArray.includes('money-chinese') ||
    hasSubformList
      ? `import { ${
          (model.listConfig.isLeftMenu &&
            model.listConfig.leftMenuConfig?.datasourceType === 'api') ||
          hasPushOrderButton
            ? 'apiConfigFunc,'
            : ''
        }${hasPrintButton ? 'generateTableJson,' : ''}${hasStartWorkButton ? 'isValidJSON,' : ''}${
          componentArray.includes('money-chinese') || hasSubformList ? 'moneyChineseData,' : ''
        }${hasSubformList ? 'camelCaseString,' : ''} } from '/@/utils/event/design';`
      : ``
  }
  ${
    hasFlowRecordButton
      ? `import FlowRecord from '/@/views/workflow/task/components/flow/FlowRecord.vue';`
      : ''
  }
  ${
    hasSubformList
      ? `
  import { FormSchema } from '/@/components/Form/src/types/form';`
      : ''
  }
  const { notification } = useMessage();
  const { t } = useI18n();
  defineEmits(['register']);
  const { filterColumnAuth, filterButtonAuth, hasPermission } = usePermission();

  const filterColumns = filterColumnAuth(columns);
  const tableRef = ref();
  const pageParamsInfo = ref<any>({});

  ${
    hasSubformList
      ? ` const innerColumns = ref({});
  const innerDataSource = ref({});
  const subFormList = ref<any[]>([]);`
      : ''
  }
  ${hasExportButton ? `const visibleExport = ref(false);` : ''}

  ${
    hasFilterButton
      ? `const isShowFilter = ref(false)
  const showColumnIndex = ref<string>('')
  const clickColumnIndex = ref<string>('')
  const filterClass = (column,filtered) => {
    return (isShowFilter.value && column.dataIndex === showColumnIndex.value) || column.dataIndex === clickColumnIndex.value || filtered ? 'show' : 'hide'
  }`
      : ''
  }
  //展示在列表内的按钮
  const actionButtons = ref<string[]>(['view', 'edit', 'copyData', 'delete', 'startwork', 'flowRecord', 'pushorder']);
  const buttonConfigs = computed(()=>{
    const list = ${JSON.stringify(model.listConfig.buttonConfigs.filter((x) => x.isUse))}
    return filterButtonAuth(list);
  })

  const tableButtonConfig = computed(() => {
    return buttonConfigs.value?.filter((x) => !actionButtons.value.includes(x.code));
  });

  const actionButtonConfig = computed(() => {
    return buttonConfigs.value?.filter((x) => actionButtons.value.includes(x.code));
  });

  const btnEvent = ${btnEvent}

  ${
    hasPushOrderButton
      ? `
  const pushFormId = ref();
  const pushBtnRef = ref(); 
  const pushbtnleft = ref(); 
  const isShowBtn = ref(false);
  const pushorderInfo = buttonConfigs.value.find((x) => x.code === 'pushorder');`
      : ''
  }
  const { currentRoute } = useRouter();
  ${
    hasTemplatePrint
      ? `const printMenuId = computed(() => currentRoute.value.meta.menuId as string);`
      : ''
  }
  const formIdComputedRef = computed(() => currentRoute.value.meta.formId as string);

  ${
    hasStartWorkButton
      ? `
  const visibleLookProcessRef = ref(false);
  const processIdRef = ref('');

  const visibleLaunchProcessRef = ref(false);
  const schemaIdRef = ref('');
  const formDataRef = ref();
  const rowKeyData = ref();
  const draftsId = ref();

  const visibleApproveProcessRef = ref(false);
  const taskIdRef = ref('');
  ${hasFlowRecordButton ? `const visibleFlowRecordModal = ref(false);` : ''}
  `
      : ''
  }
  ${model.listConfig.isLeftMenu ? `const selectId = ref('');` : ``} 
  ${model.listConfig.isLeftMenu ? `const treeData = ref<TreeItem[]>([]);` : ``} 
  ${
    hasRowCheck
      ? `
  const selectedKeys = ref<string[]>([]);
  const selectedRowsData = ref<any[]>([]);`
      : ''
  }
  ${
    isCardList
      ? `
    const datasource = ref<any>([]);`
      : ''
  }
  ${
    hasSetUserIdButton && isSetDataAuth
      ? `const [registerRuleUserModal, { openModal: openRuleUserModal }] = useModal();`
      : ''
  }
  ${
    formType === 'modal'
      ? `const [registerModal, { openModal }] = useModal();`
      : `const [registerDrawer, { openDrawer }] = useDrawer();`
  }
  ${
    hasPushOrderButton
      ? `const [registerPushModal, { openModal: openPushModal }] = useModal();`
      : ''
  }
  ${
    hasImportButton
      ? `const [registerImportModal, { openModal: openImportModal }] = useModal();`
      : ''
  }
  ${
    hasTemplatePrint
      ? `
    // 模板打印 入参参数
    let printData: {
      visible:boolean;
      id: string;
      code:string;
      requestParamsConfigs: Array<InputParamItem>;
      requestHeaderConfigs: Array<InputParamItem>;
      requestBodyConfigs: Array<InputParamItem>;
    
    } = reactive({
      visible:false,
      id: '',
      code:'',
      requestParamsConfigs: [],
      requestHeaderConfigs: [],
      requestBodyConfigs: [],
    });
    `
      : ''
  }  
  const [registerTable, { reload, ${hasFilterButton ? 'setColumns,' : ''} ${
    hasPrintButton ? 'getRawDataSource,' : ''
  } }] = useTable({
    title: '${model.listConfig?.listTitle || pascalClassName + '列表'}',
    api: get${pascalMainTableName}Page,
    rowKey: '${camelCaseString(mainTable.pkField)}',
    ${isCardList ? '' : ` columns: filterColumns,`}
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      fieldMapToTime: [${
        model.listConfig.queryConfigs
          .filter((item) => {
            return !!item.isDate;
          })
          .map(
            (item) =>
              `['${camelCaseString(item.fieldName)}', ['${
                camelCaseString(item.fieldName) + 'Start'
              }', '${camelCaseString(item.fieldName) + 'End'}'], '${
                item.format || 'YYYY-MM-DD HH:mm:ss'
              } ', true],`,
          )
          .join('\n')
        // 案例数据 ['fieldDatetime', ['fieldDatetimeStart', 'fieldDatetimeEnd'], 'YYYY-MM-DD HH:mm:ss', isString],
      }],
      showResetButton: false,
    },
    beforeFetch: (params) => {
      pageParamsInfo.value = { ...params, FormId: formIdComputedRef.value, PK: '${camelCaseString(
        mainTable.pkField,
      )}' }
      return pageParamsInfo.value;
    },
    afterFetch: (res) => {
      tableRef.value.setToolBarWidth();
      ${
        hasFilterButton
          ? `filterColumns.map((column: any) => {
        if (column.onFilter) {
          const info = res.map((item) => item[column.dataIndex!])
          column.filters = [...new Set(info)].map(item => {
            return {
              text: item,
              value: item
            }
          })
          column.customHeaderCell = () => {
              return {
                onmouseenter: () => {
                  isShowFilter.value = true
                  showColumnIndex.value = column.dataIndex
                  clickColumnIndex.value = ''
                }, 
                onmouseleave: () => {
                  isShowFilter.value = false
                  showColumnIndex.value = ''  
                }       
              };
            }
          }
        
      });
      setColumns(filterColumns);`
          : ''
      }
      ${
        hasBatchDeleteButton || hasTemplatePrint || (hasSetUserIdButton && isSetDataAuth)
          ? `
      selectedKeys.value = [];
      selectedRowsData.value = [];`
          : ''
      }
    },
    useSearchForm: ${model.listConfig.queryConfigs.length > 0 ? 'true' : 'false'},
    showTableSetting: true,
    ${hasFilterButton ? 'isFilterByDataSoure: true,' : ''}
    ${
      isCardList
        ? `
    pagination: {
      pageSizeOptions: ['9', '12', '15', '18'],
      pageSize: 9,
    },`
        : `
    striped: false,
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },`
    }
    tableSetting: {
      size: false,
      ${isCardList ? `setting: false,` : ''}
    },
    customRow,
    isAdvancedQuery:  ${model.listConfig.isAdvancedQuery ? true : false},
    querySelectOption: JSON.stringify(searchFormSchema),
    objectId: formIdComputedRef.value, ////系统表单formId,自定义表单releaseId的id值
  });

  ${
    model.listConfig.isLeftMenu || hasSubformList
      ? `
  onMounted(() => {
  ${model.listConfig.isLeftMenu ? 'fetch();' : ''}
  ${
    hasSubformList
      ? `
    subFormList.value = formProps.schemas!.filter(
        (x) => x.type === 'form' && (x.componentProps as FormSchema)!.isListView,
      );
      subFormList.value?.forEach((x) => {
        innerColumns.value[x.field] = (x.componentProps! as FormSchema)
          .viewList!.filter((sub) => sub.checked)
          .map((sub) => {
            return {
              title: sub.label,
              dataIndex: camelCaseString(sub.field),
              componentType: sub.componentType,
            };
          });
      });`
      : ''
  }
    });`
      : ''
  }
  ${
    isCardList
      ? `
  function getDataList(data) {
    datasource.value = [];
    if (data?.length) {
      data.forEach((item) => {
        let listData = {
          id: item.id,
          workflowData: item.workflowData,
        };
        for (let key in item) {
          filterColumns.forEach((col) => {
            if (col.dataIndex === key) {
              let value = item[key];
              ${
                componentArray.includes('money-chinese')
                  ? `
              if (col.componentType === 'money-chinese') {
                value = moneyChineseData(value);
              }`
                  : ''
              }
              listData[col.title as string] = {
                value,
                align: col.align,
                aRow: col.aRow,
                textBold: col.textBold,
              };
            }
          });
        }
        datasource.value.push(listData);
      });
    }
  }
  function getData(data) {
    let dataObj: any = {};
    for (let key in data) {
      if (key !== 'id' && key !== 'workflowData' && key !== 'isCanEdit') {
        dataObj[key] = data[key];
      }
    }
    return dataObj;
  }`
      : ''
  }

  function buttonClick(code) {
    ${
      hasTemplatePrint
        ? `if (code.includes(PrintButton.CODE)) {
      printData.code = code;
    }`
        : ''
    }
    btnEvent[code]();
  }
  ${
    hasAddButton
      ? `
  function handleAdd() {
    ${
      formType === 'modal'
        ? `
    openModal(true, {
      isUpdate: false,
    });`
        : ` 
    openDrawer(true, {
      isUpdate: false,
    });`
    }
  }`
      : ''
  }
  ${
    hasEditButton
      ? `
  function handleEdit(record: Recordable) {
    ${
      formType === 'modal'
        ? `
    openModal(true, {
      id: record.${camelCaseString(mainTable.pkField)},
      isUpdate: true,
    });`
        : ` 
    openDrawer(true, {
      id: record.${camelCaseString(mainTable.pkField)},
      isUpdate: true,
    });`
    }
  }`
      : ''
  }
  ${
    hasCopyDataButton
      ? `
  function handleCopyData(record: Recordable) {
    ${
      formType === 'modal'
        ? `
    openModal(true, {
      id: record.${camelCaseString(mainTable.pkField)},
      isCopy: true,
    });`
        : ` 
    openDrawer(true, {
      id: record.${camelCaseString(mainTable.pkField)},
      isCopy: true,
    });`
    }
  }`
      : ''
  }
   ${
     hasPushOrderButton
       ? `
  async function handlePushorder(record) {
    if (pushorderInfo?.setting.type === 'form') {
      pushFormId.value = record.id;
      isShowBtn.value = !isShowBtn.value;
      if (isShowBtn.value) {
        nextTick(() => {
          pushbtnleft.value = \`-\${pushBtnRef.value?.offsetWidth}px\`;
        });
      }
    } else {
      Modal.confirm({
        title: '提示信息',
        icon: createVNode(ExclamationCircleOutlined),
        content: '确定需要推单吗？',
        okText: '确认',
        cancelText: '取消',
        onOk() {
          try {
            apiConfigFunc(pushorderInfo?.setting.apiConfig, true, record).then(() => {
              notification.success({
                message: 'Tip',
                description: '执行成功',
              });
            });
          } catch (error) {}
        },
      });
    }
  }
  async function openPushorder(record) {
    isShowBtn.value = false;
    const rowInfo = await get${pascalMainTableName}(pushFormId.value); 
    openPushModal(true, {
      info: record,
      rowInfo,
    });
  }`
       : ''
   }
  ${
    hasDeleteButton || hasBatchDeleteButton
      ? `
    ${
      hasDeleteButton
        ? `
  function handleDelete(record: Recordable) {
    deleteList([record.${camelCaseString(mainTable.pkField)}]);
  }`
        : ''
    }
    ${
      hasBatchDeleteButton
        ? `
  function handleBatchdelete() {
    if (selectedKeys.value.length==0) {
      notification.warning({
        message: 'Tip',
        description: t('请选择需要删除的数据'),
      });
      return;
    }
    ${
      isCardList
        ? `
    selectedRowsData.value = datasource.value.filter((x) => selectedKeys.value.includes(x.id));`
        : ''
    }
    //与工作流相关的数据不能进行批量删除
    const cantDelete = selectedRowsData.value.filter((x) => {
      return (
        (x.workflowData?.enabled && x.workflowData?.status) ||
        (!x.workflowData?.enabled && !!x.workflowData?.processId)
      );
    });
    if (cantDelete.length) {
      notification.warning({
        message: 'Tip',
        description: t('含有不能删除的数据'),
      });
      return;
    }
    deleteList(selectedKeys.value);
  }`
        : ''
    }
  function deleteList(ids) {
    Modal.confirm({
      title: '提示信息',
      icon: createVNode(ExclamationCircleOutlined),
      content: '是否确认删除？',
      okText: '确认',
      cancelText: '取消',
      onOk() {
        delete${pascalMainTableName}(ids).then((_) => {
          handleSuccess();
          notification.success({
            message: 'Tip',
            description: t('删除成功！'),
          });
        });
      },
      onCancel() {},
    });
  }`
      : ''
  }
  ${
    hasPrintButton
      ? `
  async function handlePrint() {
    const dataSource = Array.isArray(getRawDataSource())
      ? getRawDataSource()
      : getRawDataSource().list;
    const json = generateTableJson(filterColumns, dataSource);
    const properties = filterColumns.map((item) => item.title);
    printJS({
      printable: json,
      properties: properties,
      type: 'json',
    });
  }`
      : ''
  }
  ${
    hasTemplatePrint
      ? `
    // 模板打印
		 async function handleTemplateprint() {
		      if (!selectedKeys.value.length) {
		        notification.warning({
		          message: t('提示'),
		          description: t('请选择数据'),
		        });
		        return;
		      }
		      if (selectedKeys.value.length > 1) {
		        notification.warning({
		          message: t('提示'),
		          description: t('只能选择一条数据进行操作'),
		        });
		        return;
		      }
		      let id = selectedKeys.value[0];
		      try {
		        const record = await get${pascalMainTableName}(id);
		        let res = await getPrintConfigInfo(printData.code, printMenuId.value);
            if(res.enabledMark==null){
              notification.warning({
                message: t('提示'),
                description: t('当前功能未绑定打印模板，请绑定后再进行模板打印。'),
              });
              return ;
            }
            if(res.enabledMark==0){
              notification.warning({
                message: t('提示'),
                description: t('找不到打印模板，请联系管理员。'),
              });
              return ;
            }
            printData.id = res.schemaId;
		        if (res.apiConfig) {
		          let json = JSON.parse(res.apiConfig);
		          if (json.requestParamsConfigs && json.requestParamsConfigs.length > 0) {
		            printData.requestParamsConfigs = json.requestParamsConfigs.map((ele) => {
		              if (ele.config && record[ele.config] != undefined) {
		                ele.value = record[ele.config];
		              }
		              return ele;
		            });
		          }
		          if (json.requestHeaderConfigs && json.requestHeaderConfigs.length > 0) {
		            printData.requestHeaderConfigs = json.requestHeaderConfigs.map((ele) => {
		              if (ele.config && record[ele.config] != undefined) {
		                ele.value = record[ele.config];
		              }
		              return ele;
		            });
		          }
		          if (json.requestBodyConfigs && json.requestBodyConfigs.length > 0) {
		            printData.requestBodyConfigs = json.requestBodyConfigs.map((ele) => {
		              if (ele.config && record[ele.config] != undefined) {
		                ele.value = record[ele.config];
		              }
		              return ele;
		            });
		          }
		          printData.visible = true;
		        }else{
              notification.warning({
					        message: t('提示'),
					        description: t('当前功能未绑定打印模板，请绑定后再进行模板打印。'),
					      });
            }
		      } catch (error) {}
		    }
    `
      : ''
  }
  ${
    hasFilterButton
      ? `
  function handleFilterClick(column) {
    clickColumnIndex.value = column.dataIndex
  }`
      : ''
  }
  ${
    hasRowCheck && !isCardList
      ? `
  function onSelectChange(selectedRowKeys: [], selectedRows) {
    selectedKeys.value = selectedRowKeys;
    selectedRowsData.value = selectedRows;
  }`
      : ''
  }
  function customRow(record: Recordable) {
    return {
      ${
        hasRowCheck && !isCardList
          ? `
      onClick: () => {
        let selectedRowKeys = [...selectedKeys.value];
        if (selectedRowKeys.indexOf(record.${camelCaseString(mainTable.pkField)}) >= 0) {
          let index = selectedRowKeys.indexOf(record.${camelCaseString(mainTable.pkField)});
          selectedRowKeys.splice(index, 1);
        } else {
          selectedRowKeys.push(record.${camelCaseString(mainTable.pkField)});
        }
        selectedKeys.value = selectedRowKeys;
      },`
          : ''
      }
      ondblclick: () => {
        if (record.isCanEdit && hasPermission("${lowerClassName}:edit")) {
          handleEdit(record);
        }
      },
    };
  }
  ${
    hasSetUserIdButton
      ? isSetDataAuth
        ? `
  function handleBatchSetUserId() {
    if (!selectedKeys.value.length) {
      notification.warning({
        message: 'Tip',
        description: t('请选择需要设置权限的数据'),
      });
      return;
    }
    openRuleUserModal(true, {
      rowKey: '${camelCaseString(mainTable.pkField)}', 
      columns: filterColumns,
      dataSource: selectedRowsData.value,
      setDataAuthApi: setDataAuth${pascalMainTableName}
    });
  }`
        : `
  function handleBatchSetUserId() {
    Modal.confirm({
      title: '操作失败',
      icon: createVNode(CloseCircleOutlined, { style: 'color: #ed6f6f' }),
      content: createVNode(
        'div',
        { style: 'color: #999' },
        '当前功能未配置数据权限，请配置后再进行操作。',
      ),
      centered: true,
      okText: '确定',
      cancelText: '取消',
      onOk() {},
      onCancel() {},
    });
  }`
      : ''
  }
  function handleSuccess() {
    ${
      hasRowCheck
        ? `
        selectedKeys.value = [];
        selectedRowsData.value = [];`
        : ''
    }
    reload();
  }
  ${
    hasViewButton
      ? `
  function handleView(record: Recordable) {
      ${
        formType === 'modal'
          ? `
    openModal(true, {
      isView: true,
      id: record.${camelCaseString(mainTable.pkField)},
    });`
          : ` 
    openDrawer(true, {
      isView: true,
      id: record.${camelCaseString(mainTable.pkField)},
    });`
      }
  }`
      : ''
  }
  ${
    hasExportButton
      ? `
  async function handleExport() {
    visibleExport.value = true;
  }
  async function handleExportSuccess(cols) {
    const res = await export${pascalMainTableName}({ isTemplate: false, columns: cols.toString(), ...pageParamsInfo.value});
    visibleExport.value = false;
    downloadByData(
      res.data,
      '${model.listConfig?.listTitle || pascalClassName}.xlsx',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
  }`
      : ''
  }
  ${
    hasImportButton
      ? `
  function handleImport() {
    openImportModal(true, {
      title: '快速导入',
      downLoadUrl:'/${model.outputConfig.outputValue}/${lowerClassName}/export',
    });
  }
  function handleImportSuccess(){
    reload()
  }`
      : ''
  }
  ${
    model?.listConfig?.leftMenuConfig?.treeConfig?.id
      ? `
      function handleSelectTree(treeConditions) {
        reload({ searchInfo:{treeConditions:encodeURI(JSON.stringify(treeConditions))} });
      }
      `
      : ''
  }
  ${
    model.listConfig.isLeftMenu
      ? `
  function handleSelect(selectIds) {
    selectId.value = selectIds[0];
    reload({ searchInfo: { ${camelCaseString(
      model.listConfig.leftMenuConfig!.listFieldName!,
    )}: selectIds[0] } });
  }
  async function fetch() {
    ${
      model.listConfig.leftMenuConfig?.datasourceType === `dic`
        ? `treeData.value = (await getDicDetailList({
      itemId: '${model.listConfig.leftMenuConfig.dictionaryItemId}',
    })) as unknown as TreeItem[];
    `
        : ``
    }
    ${
      model.listConfig.leftMenuConfig?.datasourceType === `static`
        ? `treeData.value = ${JSON.stringify(model.listConfig.leftMenuConfig.staticData)};`
        : ``
    }
    ${
      model.listConfig.leftMenuConfig?.datasourceType === `api`
        ? `treeData.value = (await apiConfigFunc(${JSON.stringify(
            model.listConfig.leftMenuConfig.apiConfig,
          )}, false)) as unknown as TreeItem[];`
        : ``
    }
    ${
      model.listConfig.leftMenuConfig?.datasourceType === `datasource`
        ? `treeData.value = listToTree(await getDatasourceData('${model.listConfig.leftMenuConfig.datasourceId}'), {
      id: '${model.listConfig.leftMenuConfig.relationFieldName}',
      children: 'children',
      pid: '${model.listConfig.leftMenuConfig.parentFiledName}',
    });
    `
        : ``
    }
    ${
      model.listConfig.leftMenuConfig?.childIcon || model.listConfig.leftMenuConfig?.parentIcon
        ? 'addRenderIcon(treeData.value);'
        : ''
    }
  }
  ${
    model.listConfig.leftMenuConfig?.childIcon || model.listConfig.leftMenuConfig?.parentIcon
      ? `
  function addRenderIcon(data) {
    data.map((item) => {
      if (item.children?.length) addRenderIcon(item.children);
      return (item.renderIcon = item.children?.length ? 'parentIcon' : 'childIcon');
    });
  }`
      : ``
  }`
      : ``
  }
  function getActions(record: Recordable):ActionItem[] {
    record.isCanEdit = false;
    ${
      hasStartWorkButton
        ? `
    let actionsList: ActionItem[] = [];
    let editAndDelBtn: ActionItem[] = [];
    let hasStartwork = false;
   ${hasFlowRecordButton ? `let hasFlowRecord = false;` : ''} 
    actionButtonConfig.value?.map((button) => {
      if (button.code === 'view') {
        actionsList.push({
          icon: button?.icon,
          auth: \`${lowerClassName}:\${button.code}\`,
          tooltip: button?.name,
          onClick: handleView.bind(null, record),
        });
      }
      if (['edit', 'copyData', 'delete'].includes(button.code)) {
        editAndDelBtn.push({
          icon: button?.icon,
          auth: \`${lowerClassName}:\${button.code}\`,
          tooltip: button?.name,
          color: button.code === 'delete' ? 'error' : undefined,
          onClick: btnEvent[button.code].bind(null, record),
        });
      }
      if (button.code === 'startwork') hasStartwork = true;
      ${hasFlowRecordButton ? `if (button.code === 'flowRecord') hasFlowRecord = true;` : ''}
    });
    if (record?.workflowData?.enabled) {
      //与工作流有关联的表单
      if (record?.workflowData?.status) {
        //如果是本人需要审批的数据 就会有taskIds  所以需要修改绑定事件
        const act: ActionItem = {};
        if (hasStartwork) {
          if (record?.workflowData?.taskIds) {
            act.tooltip = '查看流程(待审批)';
            act.icon = 'daishenpi|svg';
            act.onClick = handleApproveProcess.bind(null, record);
          } else {
            act.tooltip =
              '查看流程' + (record.workflowData.status === 'ACTIVE' ? '(审批中)' : '(已完成)');
            act.icon =
              record.workflowData.status === 'ACTIVE' ? 'jinshenpi|svg' : 'shenpiwancheng|svg';
            act.onClick = handleStartwork.bind(null, record);
          }
        }
        actionsList.unshift(act);
        ${
          hasFlowRecordButton
            ? `if (hasFlowRecord) {
          actionsList.splice(1, 0, {
            tooltip: '查看流转记录',
            icon: 'liuzhuanxinxi|svg',
            onClick: handleFlowRecord.bind(null, record),
          });
        }`
            : ''
        }
       
      } else {
        if (hasStartwork) {
          actionsList.unshift({
            icon: 'faqishenpi|svg',
            tooltip: record?.workflowData?.draftId ? '编辑草稿' : '发起审批' ,
            onClick: handleLaunchProcess.bind(null, record),
          });
        }
        record.isCanEdit = true;
        actionsList = actionsList.concat(editAndDelBtn);
      }
    } else {
      if (!record?.workflowData?.processId) {
        record.isCanEdit = true;
        //与工作流没有关联的表单并且在当前页面新增的数据 如选择编辑、删除按钮则加上
        actionsList = actionsList.concat(editAndDelBtn);
      }
    }
    return actionsList;
  }
    `
        : `
    const actionsList: ActionItem[] = actionButtonConfig.value?.map((button) => {
      if (!record?.workflowData?.processId) {
        record.isCanEdit = true;
        return {
          icon: button?.icon,
          auth: \`${lowerClassName}:\${button.code}\`,
          tooltip: button?.name,
          color: button.code === 'delete' ? 'error' : undefined,
          onClick: btnEvent[button.code].bind(null, record),
        };
      } else {
        if (button.code === 'view') {
          return {
            icon: button?.icon,
            auth: \`${lowerClassName}:\${button.code}\`,
            tooltip: button?.name,
            onClick: btnEvent[button.code].bind(null, record),
          };
        } else {
          return {};
        }
      }
    });
    return actionsList;
  }`
    }
  ${
    hasStartWorkButton
      ? `
  function handleStartwork(record: Recordable) {
    if (record?.workflowData) {
      visibleLookProcessRef.value = true;
      processIdRef.value = record.workflowData?.processId;
    }
  }
  ${
    hasFlowRecordButton
      ? `function handleFlowRecord(record: Recordable) {
    if (record?.workflowData) {
      visibleFlowRecordModal.value = true;
      processIdRef.value = record?.workflowData?.processId;
    }
  }`
      : ''
  }
  
  async function handleLaunchProcess(record: Recordable) {
    if (record?.workflowData) {
      if (record?.workflowData?.draftId) {
        let res = await getDraftInfo(record.workflowData.draftId);
        let data = isValidJSON(res.formData);
        if (data) {
          for (let key in data) {
            if (key.includes(formIdComputedRef.value)) {
              formDataRef.value = data[key];
            }
          }
        }
        draftsId.value = record.workflowData.draftId;
      } else {
        const result = await get${pascalMainTableName}(record['${camelCaseString(
          mainTable.pkField,
        )}']);
        formDataRef.value = result;
      }
      rowKeyData.value = record['${camelCaseString(mainTable.pkField)}'];
      visibleLaunchProcessRef.value = true;
      schemaIdRef.value = record.workflowData.schemaId;
    }
  }
  function handleApproveProcess(record: Recordable) {
    visibleApproveProcessRef.value = true;
    schemaIdRef.value = record.workflowData.schemaId;
    processIdRef.value = record.workflowData.processId;
    taskIdRef.value = record.workflowData.taskIds[0];
  }
  function handleCloseLaunch() {
    visibleLaunchProcessRef.value = false;
    reload();
  }
  function handleCloseApproval() {
    visibleApproveProcessRef.value = false;
    reload();
  }
  `
      : ''
  }
  ${
    hasSubformList
      ? `
  function getInnerDataSource(id, field) {
    if (innerDataSource.value[id]) {
      return innerDataSource.value[id][field];
    }
    return [];
  }

  async function expandedRowsChange(isOpen, record) {
    if (!isOpen) return;
    const tableInfo = await getChildren${pascalMainTableName}({
      id: record.id,
    });
    innerDataSource.value[record.id] = tableInfo;
  }`
      : ''
  }
</script>
<style lang="less" scoped>
  :deep(.ant-table-selection-col) {
    width: 50px;
  }
  .show{
    display: flex;
  }
  .hide{
    display: none !important;
  }
  ${
    isCardList
      ? `
  :deep(.vben-basic-table .ant-table .ant-table-body) {
    background: #f0f2f5;
    height: 100% !important;

    tr td {
      padding: 0 !important;
    }
  }
  :deep(.ant-table-header) {
    display: none;
  }
  :deep(.ant-table-expanded-row-fixed) {
    margin: 0 !important;
    padding: 0;
  }
  .selected-box {
    border: 1px solid #5e95ff;
    background: linear-gradient(to bottom, #88b1ff, #fff 80%);

    .box-title {
      border-bottom: 1px solid #98b7f1;

      .icon-box {
        background: #5e95ff;
        color: #fff;
      }

      .icon-delete {
        background: #ed6f6f;
      }
    }
  }
  .unselected-box {
    background: linear-gradient(to bottom, #e7efff, #fff 80%);

    .box-title {
      border-bottom: 1px solid #e3edff;

      .icon-box {
        background: #fff;
        color: #5e95ff;
      }

      .icon-delete {
        background: #fff;
        color: #ed6f6f;
      }
    }
  }
  .data-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;

    .box-container {
      margin: 0 10px 10px 0;
      flex: 0 0 calc((100% - 20px) / 3);
      height: 270px;
      overflow: auto;
      padding-bottom: 10px;

      &:nth-child(3n) {
        margin-right: 0;
      }

      .box-title {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 5px;

        .icon-box {
          padding: 4px;
          border-radius: 50%;
          margin-right: 5px;
          cursor: pointer;
        }
      }

      .flex-box {
      ${
        model.listConfig.arrangeType === 'two'
          ? `
        display: flex; 
        flex-wrap: wrap; `
          : ''
      }
        height: calc(100% - 40px);
        overflow: auto;
        padding: 10px 10px 0;

        .flex-item {
          flex: 0 0 50%;
          height: 50px;
          overflow: hidden;

          > div {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .title {
            color: #67676c;
          }

          .value {
            font-weight: bold;
            color: #4a4b4d;
          }
        }
      }
    }
  }  `
      : ''
  }
  ${
    hasPushOrderButton
      ? `
  .btn-box {
    display: grid;
    position: absolute;
    ${
      isCardList
        ? `
    top: 30px;`
        : `
    top: 0;
    left: v-bind(pushbtnleft);`
    }
    z-index: 999;

    .ant-btn {
      margin-top: 5px;
      padding: 0 10px;
    }
  }`
      : ''
  }
  ${
    hasSubformList
      ? `
  :deep(.ant-table-expanded-row) {
    .ant-table {
      margin: 0 !important;
      height: 100%;
    }
    .ant-table-wrapper {
      height: 200px;
    }
  }`
      : ''
  }
</style>`;
  return code;
}
/**
 * 构建SimpleForm页面
 * @param model
 * @returns {string}
 */
export function buildSimpleFormCode(model: GeneratorConfig, _tableInfo: TableInfo[]): string {
  const className = model.outputConfig.className;
  // const lowerClassName = lowerCase(className);
  const lowerClassName = className?.toLowerCase();
  let mainTable;
  if (model.tableConfigs && model.tableConfigs.length) {
    //数据优先
    mainTable = model.tableConfigs?.find((item) => item.isMain);
  } else {
    //界面优先、简易模板
    mainTable = model.tableStructureConfigs?.find((item) => item.isMain);
    mainTable.pkField = 'id';
  }

  if (!mainTable) {
    throw new Error('请设置主表');
  }

  const mainTableName = mainTable?.tableName;

  //将表名转为驼峰命名 首字母小写驼峰
  const camelCaseMainTableName = camelCase(mainTableName);
  //将表名转为帕斯卡命名 首字母大写的驼峰
  const pascalMainTableName = upperFirst(camelCase(camelCaseMainTableName));
  const code = `
<template>
  <div>
    <SimpleForm
      ref="systemFormRef"
      :formProps="data.formDataProps"
      :formModel="state.formModel"
      :isWorkFlow="props.fromPage!=FromPageType.MENU"
    />
  </div>
</template>
<script lang="ts" setup>
  import { reactive, ref, onMounted, nextTick } from 'vue';
  import { formProps, formEventConfigs } from './config';
  import SimpleForm from '/@/components/SimpleForm/src/SimpleForm.vue';
  import { add${pascalMainTableName}, get${pascalMainTableName}, update${pascalMainTableName} } from '/@/api/${
    model.outputConfig.outputValue
  }/${lowerClassName}';
  import { cloneDeep, isString  } from 'lodash-es';
  import { FormDataProps } from '/@/components/Designer/src/types';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { FromPageType } from '/@/enums/workflowEnum';
  import { createFormEvent, getFormDataEvent, loadFormEvent, submitFormEvent,} from '/@/hooks/web/useFormEvent';
  import { changeWorkFlowForm, changeSchemaDisabled } from '/@/hooks/web/useWorkFlowForm';
  import { WorkFlowFormParams } from '/@/model/workflow/bpmnConfig';
  import { useRouter } from 'vue-router';

  const { filterFormSchemaAuth } = usePermission();

  const RowKey = '${mainTable.pkField ? camelCase(mainTable.pkField) : 'id'}';
  const emits = defineEmits(['changeUploadComponentIds','loadingCompleted']);
  const props = defineProps({
    fromPage: {
      type: Number,
      default: FromPageType.MENU,
    },
  });
  const systemFormRef = ref();
  const data: { formDataProps: FormDataProps } = reactive({
    formDataProps: cloneDeep(formProps),
  });
  const state = reactive({
    formModel: {},
    formInfo:{formId:'',formName:''}
  });
  const { currentRoute } = useRouter();
 
  onMounted(async () => {
    try {
      if (props.fromPage == FromPageType.MENU) {
        setMenuPermission();
        if(currentRoute.value.meta){
          state.formInfo.formName = currentRoute.value.meta.title&&isString(currentRoute.value.meta.title)?currentRoute.value.meta.title:'';
          state.formInfo.formId = currentRoute.value.meta.formId&&isString(currentRoute.value.meta.formId)?currentRoute.value.meta.formId:'';
        }
        await createFormEvent(formEventConfigs, state.formModel,
          systemFormRef.value,
          formProps.schemas, true, state.formInfo.formName,state.formInfo.formId); //表单事件：初始化表单
        await nextTick();
        await loadFormEvent(formEventConfigs, state.formModel,
          systemFormRef.value,
          formProps.schemas, true, state.formInfo.formName,state.formInfo.formId); //表单事件：加载表单
      } else if (props.fromPage == FromPageType.FLOW) {
        emits('loadingCompleted'); //告诉系统表单已经加载完毕
        // loadingCompleted后 工作流页面直接利用Ref调用setWorkFlowForm方法
      } else if (props.fromPage == FromPageType.PREVIEW) {
        // 预览 无需权限，表单事件也无需执行
      } else if (props.fromPage == FromPageType.DESKTOP) {
        // 桌面设计 表单事件需要执行
        emits('loadingCompleted'); //告诉系统表单已经加载完毕
        await createFormEvent(formEventConfigs, state.formModel,
          systemFormRef.value,
          formProps.schemas, true, state.formInfo.formName,state.formInfo.formId); //表单事件：初始化表单
        await loadFormEvent(formEventConfigs, state.formModel,
          systemFormRef.value,
          formProps.schemas, true, state.formInfo.formName,state.formInfo.formId); //表单事件：加载表单
      }
    } catch (error) {}
  });
  // 根据菜单页面权限，设置表单属性（必填，禁用，显示）
  function setMenuPermission() {
    data.formDataProps.schemas = filterFormSchemaAuth(data.formDataProps.schemas!);
  }

  // 校验form 通过返回表单数据
  async function validate() {
    let values = [];
    try {
      values = await systemFormRef.value?.validate();
      //添加隐藏组件
      if (data.formDataProps.hiddenComponent?.length) {
        data.formDataProps.hiddenComponent.forEach((component) => {
          values[component.bindField] = component.value;
        });
      }
    } finally {
    }
    return values;
  }
  // 根据行唯一ID查询行数据，并设置表单数据   【编辑】
  async function setFormDataFromId(rowId) {
    try {
      const record = await get${pascalMainTableName}(rowId);
      setFieldsValue(record);
      state.formModel = record;
      await getFormDataEvent(formEventConfigs, state.formModel,
        systemFormRef.value,
        formProps.schemas, true, state.formInfo.formName,state.formInfo.formId); //表单事件：获取表单数据
    } catch (error) {
      
    }
  }
  // 辅助设置表单数据
  function setFieldsValue(record) {
    systemFormRef.value.setFieldsValue(record);
  }
  // 重置表单数据
  async function resetFields() {
    await systemFormRef.value.resetFields();
  }
  //  设置表单数据全部为Disabled  【查看】
  async function setDisabledForm() {
    data.formDataProps.schemas = changeSchemaDisabled(cloneDeep(data.formDataProps.schemas));
  }
  // 获取行键值
  function getRowKey() {
    return RowKey;
  }
  // 更新api表单数据
  async function update({ values, rowId }) {
    try {
      values[RowKey] = rowId;
      state.formModel = values;
      let saveVal = await update${pascalMainTableName}(values);
      await submitFormEvent(formEventConfigs, state.formModel,
        systemFormRef.value,
        formProps.schemas, true,  state.formInfo.formName,state.formInfo.formId); //表单事件：提交表单
      return saveVal;
    } catch (error) {}
  }
  // 新增api表单数据
  async function add(values) {
    try {
      state.formModel = values;
      let saveVal = await add${pascalMainTableName}(values);
      await submitFormEvent(formEventConfigs, state.formModel,
        systemFormRef.value,
        formProps.schemas, true,  state.formInfo.formName,state.formInfo.formId); //表单事件：提交表单
      return saveVal;
    } catch (error) {}
  }
  // 根据工作流页面权限，设置表单属性（必填，禁用，显示）
  async function setWorkFlowForm(obj: WorkFlowFormParams) { 
    try {
      state.formInfo.formId = obj.formId;
      state.formInfo.formName = obj.formName;
      let flowData = changeWorkFlowForm(cloneDeep(formProps), obj);
      let { buildOptionJson, uploadComponentIds, formModels, isViewProcess } = flowData;
      data.formDataProps = buildOptionJson;
      emits('changeUploadComponentIds', uploadComponentIds); //工作流中必须保存上传组件id【附件汇总需要】
      if (isViewProcess) {
        setDisabledForm(); //查看
      }
      state.formModel = formModels;
      setFieldsValue(formModels);
    } catch (error) {}
    await createFormEvent(formEventConfigs, state.formModel,
      systemFormRef.value,
      formProps.schemas, true, state.formInfo.formName,state.formInfo.formId); //表单事件：初始化表单
    await loadFormEvent(formEventConfigs, state.formModel,
      systemFormRef.value,
      formProps.schemas, true, state.formInfo.formName,state.formInfo.formId); //表单事件：加载表单
  }
  defineExpose({
    setFieldsValue,
    resetFields,
    validate,
    add,
    update,
    setFormDataFromId,
    setDisabledForm,
    setMenuPermission,
    setWorkFlowForm,
    getRowKey,
  });
</script>\n
  `;
  return code;
}
/**
 * 构建表单页面
 * @param model
 * @returns {string}
 */
export function buildFormCode(model: GeneratorConfig, _tableInfo: TableInfo[]): string {
  const formType = model.formJson.config.formType;
  const formWidth = model.formJson.config.formWidth;

  const code = `
<template>
${
  formType === 'modal'
    ? `
    <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit" @cancel="handleClose" :paddingRight="15" :bodyStyle="{ minHeight: '400px !important' }">
      <ModalForm ref="formRef" :fromPage="FromPageType.MENU" />
    </BasicModal>`
    : `
    <BasicDrawer showFooter v-bind="$attrs" @register="registerDrawer" :title="getTitle" @ok="handleSubmit" @cancel="handleClose">
      <ModalForm ref="formRef" :fromPage="FromPageType.MENU" />
    </BasicDrawer>`
}
  
</template>
<script lang="ts" setup>
  import { ref, computed, reactive, provide, Ref } from 'vue';
  ${
    formType === 'modal'
      ? `import { BasicModal, useModalInner } from '/@/components/Modal';`
      : `import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';`
  }
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { formProps } from './config';
  import ModalForm from './Form.vue';
  import { FromPageType } from '/@/enums/workflowEnum';

  const emit = defineEmits(['success', 'register']);

  const { notification } = useMessage();
  const formRef = ref();
  const isCopy = ref<boolean>(false)
  const state = reactive({
    formModel: {},
    isUpdate: true,
    isView: false,
    rowId: '',
  });
  provide<Ref<boolean>>('isCopy', isCopy);

  const { t } = useI18n();
  ${
    formType === 'modal'
      ? `const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    state.isUpdate = !!data?.isUpdate;
    state.isView = !!data?.isView;
    isCopy.value = !!data?.isCopy;

    setModalProps({
      destroyOnClose: true,
      maskClosable: false,
      showCancelBtn: !state.isView,
      showOkBtn: !state.isView,
      canFullscreen: true,
      width: ${formWidth || 800},
      useWrapper: true, //是否开启自适应高度
      defaultFullscreen: true, //默认全屏
    });
    if (state.isUpdate || state.isView || isCopy.value) {
      state.rowId = data.id;
      if (state.isView) {
        await formRef.value.setDisabledForm();
      }
      await formRef.value.setFormDataFromId(state.rowId);
    } else {
      formRef.value.resetFields();
    }
  });`
      : `const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
      state.isUpdate = !!data?.isUpdate;
      state.isView = !!data?.isView;
      isCopy.value = !!data?.isCopy;

      setDrawerProps({
        destroyOnClose: true,
        maskClosable: false,
        showFooter:!state.isView,
        canFullscreen: true,
        width: ${formWidth || 800},
        useWrapper: true, //是否开启自适应高度
        defaultFullscreen: true, //默认全屏
      });
      if (state.isUpdate || state.isView || isCopy.value) {
        state.rowId = data.id;
        if (state.isView) {
          await formRef.value.setDisabledForm();
        }
        await formRef.value.setFormDataFromId(state.rowId);
      } else {
        formRef.value.resetFields();
      }
    });`
  }

  const getTitle = computed(() => (state.isView ? '查看' : state.isUpdate ? '编辑' : isCopy.value ? '复制数据' : '新增'));

  async function saveModal() {
    let saveSuccess = false;
    try {
      const values = await formRef.value?.validate();
      //添加隐藏组件
      if (formProps.hiddenComponent?.length) {
        formProps.hiddenComponent.forEach((component) => {
          values[component.bindField] = component.value;
        });
      }
      if (values !== false) {
        try {
          if (!state.isUpdate || isCopy.value) {
            saveSuccess = await formRef.value.add(values);
          } else {
            saveSuccess = await formRef.value.update({ values, rowId: state.rowId });
          }
          return saveSuccess;
        } catch (error) {}
      }
    } catch (error) {
      return saveSuccess;
    }
  }

  async function handleSubmit() {
    try {
      const saveSuccess = await saveModal();
      ${
        formType === 'modal'
          ? `setModalProps({ confirmLoading: true });`
          : `setDrawerProps({ confirmLoading: true });`
      }
      if (saveSuccess) {
        if (!state.isUpdate || isCopy.value) {
          //false 新增
          notification.success({
            message: 'Tip',
            description: isCopy.value ? '复制成功' : t('新增成功！'),
          }); //提示消息
        } else {
          notification.success({
            message: 'Tip',
            description: t('修改成功！'),
          }); //提示消息
        }
        ${formType === 'modal' ? `closeModal();` : `closeDrawer();`}
        formRef.value.resetFields();
        emit('success');
      }
    } finally {
      ${
        formType === 'modal'
          ? `setModalProps({ confirmLoading: false });`
          : `setDrawerProps({ confirmLoading: false });`
      }
    }
  }
    
  function handleClose() {
    formRef.value.resetFields();
  }
</script>\n
  `;
  return code;
}

/**
 * 构建表单页面FormSchema 与 列表页面BasicColumn
 * @param model
 * @param formSchema
 */
export function buildConfigJsonCode(model: GeneratorConfig, formProps: FormProps): string {
  let mainTable;
  if (model.tableConfigs && model.tableConfigs.length) {
    //数据优先
    mainTable = model.tableConfigs?.find((item) => item.isMain);
  } else {
    //界面优先、简易模板
    mainTable = model.tableStructureConfigs?.find((item) => item.isMain);
  }
  const findUpload = (component) => {
    const hasSubComponent = ['tab', 'grid', 'card', 'form', 'one-for-one'];
    return component?.some((info) => {
      if (hasSubComponent.includes(info.type!)) {
        if (info.type === 'form') {
          return info.componentProps.columns?.some((childInfo) => {
            return childInfo.componentType === 'Upload';
          });
        } else if (info.type === 'one-for-one') {
          return info.componentProps.childSchemas?.some((childInfo) => {
            if (childInfo.children) {
              return childInfo.children?.some((childSubInfo) => {
                let hasUpload = childSubInfo.list.some((com) => com.type === 'upload');
                if (hasUpload) return true;
                hasUpload = findUpload(childSubInfo.list);
                return hasUpload;
              });
            } else {
              return childInfo.type === 'upload';
            }
          });
        } else {
          return info.children?.some((childInfo) => {
            let hasUpload = childInfo.list.some((com) => com.type === 'upload');
            if (hasUpload) return true;
            hasUpload = findUpload(childInfo.list);
            return hasUpload;
          });
        }
      } else if (info.type === 'table-layout') {
        return info.children?.some((childSubInfo) => {
          return childSubInfo.list.some((com) => {
            return findUpload(com.children);
          });
        });
      } else {
        return info.type === 'upload';
      }
    });
  };

  if (!mainTable) {
    throw new Error('请设置主表');
  }

  //将表名转为驼峰命名 首字母小写驼峰
  //将表名转为帕斯卡命名 首字母大写的驼峰
  // querySelectOption
  const code = `
  import { FormProps, FormSchema } from '/@/components/Form';
  import { BasicColumn } from '/@/components/Table';
  ${findUpload(formProps.schemas) ? `import { uploadApi } from '/@/api/sys/upload';` : ''}
  ${
    model.listConfig.isAdvancedQuery
      ? `export const searchFormSchema: FormSchema[] = [
    ${JSON.parse(model.listConfig.querySelectOption)
      .map((tempEle) => {
        return {
          fieldName: tempEle.value,
          isDate: ['time', 'date'].includes(tempEle.type) || !!tempEle.isDate,
        };
      })
      .map((item) => {
        const schema = findSchema(formProps.schemas, camelCaseString(item.fieldName));
        const [isNeedTrans, option] = whetherNeedToTransform(item, model.formJson.list);
        return handleSearchForm(option, schema, item, isNeedTrans);
      })
      .join('\n')}
  ];`
      : `
      export const searchFormSchema: FormSchema[] = [
    ${model.listConfig.queryConfigs
      .map((item) => {
        const schema = findSchema(formProps.schemas, camelCaseString(item.fieldName));
        const [isNeedTrans, option] = whetherNeedToTransform(item, model.formJson.list);
        return handleSearchForm(option, schema, item, isNeedTrans);
      })
      .join('\n')}
  ];
    `
  }
  export const columns: BasicColumn[] = [
    ${model.listConfig.columnConfigs
      .map((item) => {
        console.log('item: ', item);
        return `
      {
        resizable: true,
        dataIndex: '${camelCaseString(item.columnName)}',
        title: '${item.label}',
        componentType:'${item.componentType}',
        ${item.alignType ? `align: '${item.alignType}',` : ''}
        ${item.isTotal ? 'total: true,' : ''}
        ${!item.autoWidth && item.columnWidth ? `width:${item.columnWidth},` : ''}
        ${
          item.componentProps?.datasourceType === 'staticData'
            ? `
        customRender: ({ record }) => {
          const staticOptions = ${JSON.stringify(item.componentProps?.staticOptions)};
          ${
            item.componentType === 'checkbox'
              ? `
            const valArr = record.${camelCaseString(item.columnName)}?.split(',');
            return staticOptions
              .filter((x) => valArr.includes(x.value))
              ?.map((x) => x.label)
              .toString();`
              : `
          return staticOptions.filter((x) => x.value === record.${camelCaseString(
            item.columnName,
          )})[0]?.label;`
          }
        },`
            : ''
        }
        ${
          item.isFilter
            ? `onFilter: (value: string, record) => record.${camelCaseString(
                item.columnName,
              )} === value,`
            : ''
        }
        sorter: true,
        ${
          model.listConfig.listStyle === 'card'
            ? `
        textBold: ${item.textBold || false},
        aRow: ${item.aRow || false},`
            : ''
        }
        styleConfig:${JSON.stringify(item.componentProps?.styleConfig)},
        listStyle:${JSON.stringify(item.componentProps?.listStyle)}
      },
      `;
      })
      .join('\n')}
  ];
  //表单事件
  export const formEventConfigs = ${JSON.stringify(model.formEventConfig)};
  export const formProps: FormProps = ${JSON.stringify(formProps, (key, value) => {
    if (key === 'api') {
      //TODO  后续新增API 这里也要修改
      if (value.toString().includes('uploadApi')) {
        return `#{upload}#`;
      } else {
        return value;
      }
    }
    return value;
  })};\n
  //左侧树结构配置
  export const treeConfig = ${JSON.stringify(
    model?.listConfig?.leftMenuConfig?.treeConfig
      ? model?.listConfig?.leftMenuConfig?.treeConfig
      : '',
  )};\n
  `;
  return formatCode(code.replace(`"#{upload}#"`, 'uploadApi'));
}

/**
 * 构建工作流权限配置文件
 * @param formProps
 */
export function buildWorkflowPermissionConfigJsonCode(formProps: FormProps | AppFormProps): string {
  const workFlowConfigJson = getWorkflowPermissionConfig(formProps.schemas);
  const code = `
  export const permissionList = ${JSON.stringify(workFlowConfigJson)};\n
  `;
  return formatCode(code);
}
/**
 * 构建app 的api代码
 * @param model 配置
 * @param _tableInfo
 * @returns
 */
export function buildAppApiCode(model: GeneratorConfig): string {
  const className = model.outputConfig.className;
  const lowerClassName = className?.toLowerCase();

  let mainTable;
  if (model.tableConfigs && model.tableConfigs.length) {
    //数据优先
    mainTable = model.tableConfigs?.find((item) => item.isMain);
  } else {
    //界面优先、简易模板
    mainTable = model.tableStructureConfigs?.find((item) => item.isMain);
    mainTable.pkField = 'id';
  }

  if (!mainTable) {
    throw new Error('请设置主表');
  }

  const mainTableName = mainTable?.tableName;

  //将表名转为驼峰命名 首字母小写驼峰
  const camelCaseMainTableName = camelCase(mainTableName);
  //将表名转为帕斯卡命名 首字母大写的驼峰
  const pascalMainTableName = upperFirst(camelCase(camelCaseMainTableName));

  const code = `
import {
  http
} from '@/common/request/index.js'; // 局部引入

const api = {
  Page : '/${model.outputConfig.outputValue}/${lowerClassName}/page',
  List : '/${model.outputConfig.outputValue}/${lowerClassName}/list',
  Info : '/${model.outputConfig.outputValue}/${lowerClassName}/info',
  Add : '/${model.outputConfig.outputValue}/${lowerClassName}/add',
  Update : '/${model.outputConfig.outputValue}/${lowerClassName}/update',
  Delete : '/${model.outputConfig.outputValue}/${lowerClassName}/delete'
}

/**
 * 根据参数  查询${pascalMainTableName}分页列表
 * @param {Object} params - 查询参数  
 */
export const get${pascalMainTableName}Page = (params) => {
  return http.get(api.Page, {
    params
  })
}

  
/**
 * 根据参数 查询${pascalMainTableName}列表
 * @param {Object} params - 查询参数  
 */
export const get${pascalMainTableName}List = (params) => {
  return http.get(api.List, {
    params
  })
}

/**
 * 获取${pascalMainTableName}信息
 * @param {Object} params - id  
 */
export const get${pascalMainTableName} = (id) => {
  return http.get(api.Info, {
    params: { id },
  })
}

/**
 * 新增${pascalMainTableName}
 * @param {Object} params - 表单数据  
 */
export const add${pascalMainTableName} = (formData) => {
  return http.post(api.Add, formData)
}

/**
 * 修改${pascalMainTableName}
 * @param {Object} params - 表单数据  
 */
export const update${pascalMainTableName} = (formData) => {
  return http.post(api.Update, formData)
}

/**
 * 删除${pascalMainTableName}（批量删除）
 * @param {Object} params - 表单数据  
 */
export const delete${pascalMainTableName} = (ids) => {
  return http.post(api.Delete, ids)
}
`;
  return code;
}

/**
 * 构建表单页面FormSchema 与 列表页面BasicColumn
 * @param model
 * @param formSchema
 */
export function buildAppConfigJsonCode(
  model: GeneratorConfig,
  formProps: AppFormProps,
  designType: string,
): string {
  const className = model.outputConfig.className;
  // const lowerClassName = lowerCase(className);
  const lowerClassName = className?.toLowerCase();
  // const pascalClassName = upperFirst(camelCase(className));

  let mainTable;
  if (designType == 'data') {
    //数据优先
    mainTable = model.tableConfigs?.find((item) => item.isMain);
  } else {
    //界面优先、简易模板
    mainTable = model.tableStructureConfigs?.find((item) => item.isMain);
  }

  if (!mainTable) {
    throw new Error('请设置主表');
  }

  const mainTableName = mainTable?.tableName;

  //将表名转为驼峰命名 首字母小写驼峰
  const camelCaseMainTableName = camelCase(mainTableName);
  //将表名转为帕斯卡命名 首字母大写的驼峰
  const pascalMainTableName = upperFirst(camelCase(camelCaseMainTableName));
  //将表名转为驼峰命名 首字母小写驼峰
  //将表名转为帕斯卡命名 首字母大写的驼峰

  let categoryConfigs: any = '';
  if (model.listConfig.isLeftMenu) {
    const { datasourceType, listFieldName, childIcon, parentIcon, menuName } =
      model.listConfig.leftMenuConfig!;
    categoryConfigs = {
      datasourceType: datasourceType,
      listFieldName: listFieldName ? camelCaseString(listFieldName) : '',
      childIcon: childIcon,
      parentIcon: parentIcon,
      menuName: menuName,
    };
    if (datasourceType == 'dic') {
      categoryConfigs.dictionaryItemId = model.listConfig.leftMenuConfig?.dictionaryItemId;
    } else if (datasourceType == 'static') {
      categoryConfigs.staticData = model.listConfig.leftMenuConfig?.staticData;
    } else if (datasourceType == 'api') {
      categoryConfigs.apiConfig = setApiConfig(model.listConfig.leftMenuConfig?.apiConfig);
    }
  }

  const code = `
import { 
  get${pascalMainTableName}Page,
  delete${pascalMainTableName} 
} from '@/common/api/${model.outputConfig.outputValue}/${lowerClassName}/index.js';
import {
  componentType,
  datasourceTypeEnum
} from '@/components/simple-form/types/form.js'


export const listProps = {
  rowKey:'${designType == 'data' ? camelCase(mainTable.pkField) : 'id'}',
  //列表请求接口
  api: get${pascalMainTableName}Page,
  // 请求之前处理参数 (params) => {}
  beforeFetch: (params) => {
    for(let key in params){
			if(key.includes(',')){
				delete params[key]
			}
		}
    params.PK='${designType == 'data' ? camelCase(mainTable.pkField) : 'id'}'
		params.FormId='${model.formId}'
    return params
  },
  // 自定义处理接口返回参数
  afterFetch: (data) => {
    return data
  },
  //数据源  静态
  datasource: [],
  workflowConfig:{
    Pk:'${designType == 'data' ? camelCase(mainTable.pkField) : 'id'}',
    Formid:'${model.formId}'
  },
  //列配置
  columnConfigs: [
    ${model.listConfig.columnConfigs
      .map((item) => {
        return `{
        title: '${item.label}',
        mainField:${item.mainField},
			  showLabel:${item.showLabel},
        componentType:'${buildAppComponentType(item.componentType!)}',
        field: '${camelCaseString(item.columnName)}',
      }`;
      })
      .join(',')}
  ],
  //是否启用搜索
  isSearch: true,
  //是否分页
  isPage: true,
  //分类筛选配置
  categoryConfigs:${JSON.stringify(categoryConfigs)},
  //搜索配置
  searchConfigs: [${model.listConfig.queryConfigs
    .map((item) => {
      const schema = findAppSchema(formProps.schemas, camelCaseString(item.fieldName));

      const [isNeedTrans, option] = whetherNeedToTransform(item, model.formJson.list);
      return handleAppSearchForm(option, schema, item, isNeedTrans);
    })
    .join('\n')}],
  //表单页面地址
  formUrl: '/pages/${model.outputConfig.outputValue}/${lowerClassName}/container',
  //列表页面地址
  listUrl: '/pages/${model.outputConfig.outputValue}/${lowerClassName}/list',
  //按钮配置
  otherButtons:[${model.listConfig.buttonConfigs
    .filter((item) => item.code !== 'edit' && item.code !== 'delete')
    .map((item) => {
      return `
        {
          text: '${item.name}',
          isUse:${item.isUse},
          code:'${item.code}',
        }`;
    })
    .join(',')}],
  buttonConfigs: [${getButtonConfigs(model.listConfig.buttonConfigs, pascalMainTableName).join(
    ',',
  )}],
  formEventConfig:${JSON.stringify(getFormEvents(cloneDeep(model.formEventConfig)))}
}


export const formProps =  ${JSON.stringify(formProps, (key, value) => {
    if (key === 'api') {
      //TODO  后续新增API 这里也要修改
      if (value.toString().includes('uploadApi')) {
        return `#{upload}#`;
      } else {
        return value;
      }
    }
    return value;
  })};\n
`;
  return code.replace(`"#{upload}#"`, 'uploadApi');
}
function getFormEvents(formEvents) {
  for (const item in formEvents) {
    formEvents[item] = formEvents[item].filter(
      (x) => x.nodeInfo && x.nodeInfo.processEvent?.length > 0,
    );
  }
  return formEvents;
}
function getButtonConfigs(buttonConfigs, pascalMainTableName) {
  const codes = ['view', 'add', 'edit', 'delete'];
  const buttons = buttonConfigs.filter((item) => item.isUse);
  const others = buttons.filter((item) => !codes.includes(item.code));
  const temp: any[] = [];
  buttons.forEach((item) => {
    if (item.code === 'edit') {
      temp.push(`{
        code: '${item.code}',
        icon: 'ant-design:edit-square',
        color:'#5E95FF',
        text: '${item.name}',
        action: (record) =>{
          uni.navigateTo({
            url: listProps.formUrl + '?type=edit&id=' + record[listProps.rowKey],
          });
        }
      }`);
    }
    if (item.code === 'delete') {
      temp.push(`{
        code: '${item.code}',
        icon: 'ant-design:delete-outlined',
        color:'#EF6969',
        text: '${item.name}',
        action: async (record, { reload }) => {
          await delete${pascalMainTableName} ([record[listProps.rowKey]]);
          reload()
          //请求删除接口
          uni.showToast({
            title: "删除成功"
          })
        } 
      }`);
    }
  });
  if (others.length > 0) {
    temp.push(`{
      code: 'more',
      icon: 'ant-design:lipsis-outlined',
      color: '#5E95FF',
      text: '更多',
      buttons: ${JSON.stringify(others)},
      action: async (record, { showMoreButton }) => {
        showMoreButton(record[listProps.rowKey])
      },
    }`);
  }
  return temp;
}
/**
 * 构建app列表代码
 * @param model
 */
export function buildAppListCode(_model: GeneratorConfig): string {
  const code = `
<template>
	<SimpleList ref="listRef" :routeTitle="routeTitle"  :listProps="listProps"></SimpleList>
</template>

<script setup>
import { ref } from 'vue';
import { onReachBottom, onPullDownRefresh,onShow } from '@dcloudio/uni-app'; //不支持onLoad
import SimpleList from '@/components/simple-list/SimpleList.vue';
import { listProps } from './config/index.js'



onReachBottom(() => {
	// 必须要这个事件 可以不写逻辑 
});
const listRef = ref(); 
const firstLoad = ref(true);
const routeTitle = ref('列表');
onShow(()=>{
  const pages = getCurrentPages();
	if(pages&&Array.isArray(pages)&&pages.length>0){
		const currentPage = pages[pages.length - 1];
		if(currentPage&&currentPage.$page&&currentPage.$page.meta&&currentPage.$page.meta.navigationBar&&currentPage.$page.meta.navigationBar.titleText){
			const title = currentPage.$page.meta.navigationBar.titleText;
			routeTitle.value = title;
		}
	}
	if (firstLoad.value) {
		firstLoad.value = !firstLoad.value;
		return;
	}
	listRef.value.reload();
}) 
</script>

<style></style>

  
  `;
  return code;
}
/**
 * 构建app表单页代码
 * @param model
 * @param _tableInfo
 * @returns
 */
export function buildAppFormCode(model: GeneratorConfig): string {
  const className = model.outputConfig.className;
  // const lowerClassName = lowerCase(className);
  const lowerClassName = className?.toLowerCase();
  // const pascalClassName = upperFirst(camelCase(className));
  let mainTable;
  if (model.tableConfigs && model.tableConfigs.length) {
    //数据优先
    mainTable = model.tableConfigs?.find((item) => item.isMain);
  } else {
    //界面优先、简易模板
    mainTable = model.tableStructureConfigs?.find((item) => item.isMain);
    mainTable.pkField = 'id';
  }

  if (!mainTable) {
    throw new Error('请设置主表');
  }

  const mainTableName = mainTable?.tableName;

  //将表名转为驼峰命名 首字母小写驼峰
  const camelCaseMainTableName = camelCase(mainTableName);
  //将表名转为帕斯卡命名 首字母大写的驼峰
  const pascalMainTableName = upperFirst(camelCase(camelCaseMainTableName));

  const code = `
<template>
	<view>
    <SimpleForm ref="formRef" :key="renderKey" :formProps="formConfig.formProps" :disabled="disabled"  :control="formConfig.type"></SimpleForm>
	</view>
</template>

<script setup>
import { ref, reactive,onMounted } from 'vue';
import SimpleForm from '@/components/simple-form/SimpleForm.vue';
import { formProps } from './config/index.js';
import { FromPageType } from "@/common/enums/form.ts";
import { listProps } from './config/index.js'
import { add${pascalMainTableName}, update${pascalMainTableName}, get${pascalMainTableName} } from '@/common/api/${model.outputConfig.outputValue}/${lowerClassName}/index.js';
import { cloneDeep } from '@/utils/helper/utils.js';
import { handlerFormPermission } from "@/common/hooks/form.ts";
import { setFormPropsAuth } from '@/utils/simpleForm/changeJson.js'
const renderKey = ref(0);
const props = defineProps({
  disabled: {
    type:Boolean,
    default:false
  },
  type: {
    type:String,
    default:FromPageType.ADD
  },
  id: {
    type:String,
    default:''
  },
  isWorkFlow:{
    type:Boolean,
    default:false
  },
  formModel: {
    type:Object
  },
  workFlowParams: {
    type:Object
  }
});
const rowKey = listProps.rowKey;
const formConfig = reactive({
  formProps:formProps,
  id:"",
  uploadComponentIds:[],
  type:FromPageType.ADD
});
const formRef = ref();
onMounted(async () => {
  formConfig.id = props.id;
  formConfig.type = props.type;
  if(props.isWorkFlow){
    formConfig.type = props.workFlowParams.type;
    if( props.formModel&&props.formModel[rowKey]){
      formConfig.type = FromPageType.EDIT;
      formConfig.id = props.formModel[rowKey];
    }
    if(props.disabled) formConfig.type = FromPageType.VIEW;
    setWorkflowFormData(formProps);
  }else{
    let auth=uni.getStorageSync('MenuAuth')
		let formAuth=auth.formAuthCode||[]; 
		setFormPropsAuth(formConfig.formProps.schemas,formAuth)
    await setForm();
  }
})

async function setForm(){
  //  编辑
  if (formConfig.type == FromPageType.EDIT||formConfig.type  == FromPageType.VIEW) { 
      const {
        data
      } = await get${pascalMainTableName}(formConfig.id);
      if (data) {
        await formRef.value.setFormData({...data,...props.formModel})
      }
    }
    //新增
    else if (formConfig.type  == FromPageType.ADD) { 
      await formRef.value.init()
    }
    if(formConfig.type== FromPageType.EDIT){
      //获取表单数据
      if(listProps.formEventConfig&&listProps.formEventConfig[1]){
        formRef.value.executeFormEvent(listProps.formEventConfig[1],false);
      } 
    }

    //初始化表单
    if(listProps.formEventConfig&&listProps.formEventConfig[0]){
      formRef.value.executeFormEvent(listProps.formEventConfig[0],false);
    } 
    //加载表单
    if(listProps.formEventConfig&&listProps.formEventConfig[2]){
      formRef.value.executeFormEvent(listProps.formEventConfig[2],false);
    } 
}
// 工作流设置表单数据
async function setWorkflowForm(){
  if(props.isWorkFlow){
    setWorkflowFormData(formProps);
  }
  // formConfig.type
  renderKey.value++;
  await setForm();
}
// 工作流辅助设置表单数据
function setWorkflowFormData(formProps){
  let options = cloneDeep(formProps);
  let otherParams = {...props.workFlowParams.otherParams,uploadComponentIds:formConfig.uploadComponentIds}
  let  obj = handlerFormPermission(
          options,
          props.workFlowParams.formConfigChildren,
          props.workFlowParams.formConfigKey,
          otherParams
          );
  formConfig.formProps = obj.buildOptionJson;
  formConfig.uploadComponentIds = obj.otherParams.uploadComponentIds;
}
// 获取上传组件Id集合
function getUploadComponentIds(){
  return formConfig.uploadComponentIds
}
// 校验
async function validate() {
	await formRef.value.validate();
}
// 提交
 async function submit() {
  let formModelIdVal = {};
	try {
		await formRef.value.validate();
    const formData = await formRef.value.getFormData();
		if(formConfig.type == FromPageType.EDIT){
      formData[listProps.rowKey]=formConfig.id;
			await update${pascalMainTableName}(formData);
      formModelIdVal[listProps.rowKey]=formConfig.id;
		}
		else{
			let res  = await add${pascalMainTableName}(formData);
      formModelIdVal[listProps.rowKey]=res.data;
		}
	} catch (err) {
		console.log(err);
	}
  //提交表单事件
  if(listProps.formEventConfig&&listProps.formEventConfig[3]){
    formRef.value.executeFormEvent(listProps.formEventConfig[3],false);
  } 
  return formModelIdVal;
}


// 重置
async function reset() {
	await formRef.value.reset();
}
// 表单初始化
 async function init() {
	await formRef.value.init();
}
// 设置表单数据
 async function setFormData(formModels) {
	await formRef.value.setFormData(formModels);
}
// 获取表单数据
async function getFormData() {
	let formModelObj =  await formRef.value.getFormData();
  if(formConfig.type == FromPageType.EDIT||formConfig.type == FromPageType.VIEW){
      formModelObj[listProps.rowKey]=formConfig.id;
	}
  return formModelObj;
}
defineExpose({
    init,
		submit,
    reset,
		setFormData,
		validate,
		getFormData,
    setWorkflowForm,
    getUploadComponentIds
  });
</script>

<style>
page{
  background: #fff;
}
</style>
`;
  return code;
}

// 构建表单菜单页容器页面
export function buildAppContainerCode(): string {
  return `
<template>
  <view>
    <PageHead :title="routeTitle" backUrl=""></PageHead>
    <view  class="form-container">
      <view class="form-box">
        <Form ref="formRef"  :disabled="formConfig.isView" :id="formConfig.id"  :type="formConfig.type"></Form>
        <view v-if="!formConfig.isView" class="form-btn-box">
          <button type="default" class="button" @click="reset">重置</button>
          <button type="primary" class="button" @click="submit">确定</button>		
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
    import { ref, reactive } from 'vue';
    import Form from './form.vue';
    import { onLoad } from '@dcloudio/uni-app'; 
    import PageHead from "@/components/layout/PageHead.vue";
    import { FromPageType } from "@/common/enums/form.ts";
    import { getFormTitle } from "@/common/hooks/form.ts";
    import { listProps } from './config/index.js'
    const formRef = ref(); //表单ref
    const routeTitle = ref('表单信息');
    const formConfig = reactive({
      type:FromPageType.ADD,
      id:'',
      isView:false
    });
    onLoad(async (option) => {
      formConfig.id = option.id??'';
      formConfig.type = option.type;
      let title = getFormTitle(formConfig.type);
      routeTitle.value =title;
      uni.setNavigationBarTitle({title:title});
      formConfig.isView = false;
      if(formConfig.type==FromPageType.VIEW){
        formConfig.isView = true;
      }
    });
    async function submit(){
      let saveVal = await formRef.value.submit();
      if(saveVal){
        if(formConfig.type==FromPageType.ADD){
          uni.showToast({
            title: '新增成功'
          });
        }else  if(formConfig.type==FromPageType.EDIT){
          uni.showToast({
            title: '修改成功'
          });
        }
        uni.navigateTo({
          url: listProps.listUrl
        });
      }
    }
    async function reset(){
      formRef.value.reset();
    }
</script>

<style>
  page{
    background: #fff;
  }
</style>
`;
}
// 生成tag
export function buildTagStringCode(model: GeneratorConfig): string {
  const className = model.outputConfig.className;
  const lowerClassName = className?.toLowerCase();
  return `
	<sys-${model.outputConfig.outputValue}-${lowerClassName} ref="systemRef" v-else-if="componentName=='sys-${model.outputConfig.outputValue}-${lowerClassName}'" :disabled="props.disabled"  :isWorkFlow="true"   :workFlowParams="formConfig.workFlowParams"  :formModel="props.formModel"></sys-${model.outputConfig.outputValue}-${lowerClassName}>
  <!--html--> 
  `;
}
/**
 * 当前搜索项 是否需要转换
 * @param model 配置
 */
export function whetherNeedToTransform(
  queryConfig: QueryConfig,
  components: ComponentOptionModel[],
): [boolean, ComponentConfigModel | undefined] {
  const layoutComponents = ['tab', 'grid', 'card'];
  let returnTransform: [boolean, ComponentConfigModel | undefined] = [false, undefined];
  components?.some((info) => {
    if (layoutComponents.includes(info.type!)) {
      const hasComponent = info?.layout?.some((childInfo) => {
        const layoutChildOption = childInfo.list.find(
          (com) => com.bindField === queryConfig.fieldName,
        );
        if (!!layoutChildOption) {
          returnTransform = transformComponent.includes(layoutChildOption.type)
            ? [true, layoutChildOption.options]
            : [false, undefined];
          return true;
        }
        if (!childInfo.list.length) return false;
        const transformCom = whetherNeedToTransform(queryConfig, childInfo.list);
        if (!!transformCom[0]) {
          returnTransform = transformCom;
          return true;
        }
        return false;
      });
      return hasComponent;
    } else if (info.type === 'table-layout') {
      let hasComponent = false;
      info?.layout?.map((childInfo) => {
        childInfo.list.map((child) => {
          const layoutChildOption = child.children?.find(
            (com) => com.bindField === queryConfig.fieldName,
          );
          if (!!layoutChildOption) {
            returnTransform = transformComponent.includes(layoutChildOption.type)
              ? [true, layoutChildOption.options]
              : [false, undefined];
            hasComponent = true;
          }
          if (!child.children?.length) hasComponent = false;
          const transformCom = whetherNeedToTransform(queryConfig, child.children!);
          if (!!transformCom[0]) {
            returnTransform = transformCom;
            hasComponent = true;
          }
          hasComponent = false;
        });
      });
      return hasComponent;
    } else {
      const option = components.find((item) => item?.bindField === queryConfig.fieldName);
      if (!!option) {
        returnTransform = transformComponent.includes(option.type)
          ? [true, option?.options]
          : [false, undefined];
      }
      return !!option;
    }
  });
  return returnTransform;
}

export function findSchema(schemaArr, fieldName) {
  let schema;
  const formListComponent = ['tab', 'grid', 'card'];
  schemaArr?.some((info) => {
    if (formListComponent.includes(info.type!)) {
      const hasComponent = info.children.some((childInfo) => {
        schema = childInfo.list.find((com) => com.field === fieldName);
        if (!!schema) return true;
        schema = findSchema(childInfo.list, fieldName);
        return !!schema;
      });
      return !!hasComponent;
    } else if (info.type == 'table-layout') {
      const hasComponent = info.children.some((childInfo) => {
        return childInfo.list.some((child) => {
          schema = child.children.find((com) => com.field === fieldName);
          if (!!schema) return true;
          schema = findSchema(child.children, fieldName);
          return !!schema;
        });
      });
      return !!hasComponent;
    } else {
      schema = info.field === fieldName ? info : null;
      return !!schema;
    }
  });
  return schema;
}
export function findAppSchema(schemaArr, fieldName) {
  let schema;
  const formListComponent = ['Tab', 'Segmented', 'Collapse', 'TableLayout'];
  schemaArr?.some((info) => {
    if (formListComponent.includes(info.component)) {
      const hasComponent = info.layout.some((childInfo) => {
        schema = childInfo.children.find((com) => com.field === fieldName);
        if (!!schema) return true;
        schema = findAppSchema(childInfo.children, fieldName);
        return !!schema;
      });
      return !!hasComponent;
    } else {
      schema = info.field === fieldName ? info : null;
      return !!schema;
    }
  });
  return schema;
}
const hasButton = (list, code) => {
  return list.filter((x) => x.code === code && x.isUse).length > 0;
};

/**
 * 判断是否存在远程组件 使用 数据字典
 * @param components 判断是否存在远程组件 使用 数据字典
 * @param type dic datasource api
 * @returns
 */
// export function existRemoteComponent(components: ComponentOptionModel[], type: string): boolean {
//   const idx = components.findIndex(
//     (item) => transformComponent.includes(item.type) && item.options.datasourceType === type,
//   );
//   if (idx > -1) {
//     return true;
//   }

//   const tabComponents = components.filter((item) => item.type === 'tab');
//   if (tabComponents && tabComponents.length > 0) {
//     let layoutChildOption;
//     for (const tabComp of tabComponents) {
//       if (tabComp.layout) {
//         for (const ly of tabComp.layout) {
//           layoutChildOption = ly.list.find(
//             (item) =>
//               transformComponent.includes(item.type) && item.options.datasourceType === type,
//           );
//           break;
//         }
//       }
//     }
//     if (layoutChildOption && transformComponent.includes(layoutChildOption.type)) {
//       return true;
//     } else {
//       return false;
//     }
//   }

//   const gridComponents = components.filter((item) => item.type === 'grid');
//   if (gridComponents && gridComponents.length > 0) {
//     let layoutChildOption;
//     for (const gridComp of gridComponents) {
//       if (gridComp.layout) {
//         for (const ly of gridComp.layout) {
//           layoutChildOption = ly.list.find(
//             (item) =>
//               transformComponent.includes(item.type) && item.options.datasourceType === type,
//           );
//           break;
//         }
//       }
//     }
//     if (layoutChildOption && transformComponent.includes(layoutChildOption.type)) {
//       return true;
//     } else {
//       return false;
//     }
//   }
//   return false;
// }

//需要转换为非输入框的组件
const transformComponent = [
  'number',
  'radio',
  'checkbox',
  'select',
  'cascader',
  'associate-select',
  'associate-popup',
  'multiple-popup',
  'area',
  'switch',
  'time',
  'date',
  'slider',
  'rate',
  'computational',
  'money-chinese',
  'info',
  'organization',
  'picker-color',
  'user',
  'tree-select-component',
];
