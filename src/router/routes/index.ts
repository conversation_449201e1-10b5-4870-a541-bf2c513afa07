import type { AppRouteRecordRaw, AppRouteModule } from '/@/router/types';

import {
  PAGE_NOT_FOUND_ROUTE,
  REDIRECT_ROUTE,
  SYSTEM_ROUTE,
  USERCENTER_ROUTE,
  // IM_ROUTE,
  // CUSTOMFORM_ROUTE,
} from '/@/router/routes/basic';

import { mainOutRoutes } from './mainOut';
import { PageEnum } from '/@/enums/pageEnum';
import { t } from '/@/hooks/web/useI18n';

const modules = import.meta.glob('./modules/**/*.ts', { eager: true });

const routeModuleList: AppRouteModule[] = [];

Object.keys(modules).forEach((key) => {
  const mod = modules[key].default || {};
  const modList = Array.isArray(mod) ? [...mod] : [mod];
  routeModuleList.push(...modList);
});

export const asyncRoutes = [PAGE_NOT_FOUND_ROUTE, ...routeModuleList];

export const RootRoute: AppRouteRecordRaw = {
  path: '/',
  name: 'Root',
  redirect: PageEnum.BASE_HOME,
  meta: {
    title: 'Root',
  },
};

export const LoginRoute: AppRouteRecordRaw = {
  path: '/login',
  name: 'Login',
  component: () => import('/@/views/sys/login/Login.vue'),
  meta: {
    title: t('登录页'),
  },
};

export const QuestionnaireRoute: AppRouteRecordRaw = {
  path: '/questionnaireForm/:id/:from',
  name: 'Questionnaire',
  component: () => import('/@/views/questionnaire/template/components/TemplateForm.vue'),
  meta: {
    title: t('调查问卷'),
    ignoreAuth: true,
    carryParam: true,
  },
};

export const dayReportRoute: AppRouteRecordRaw = {
  path: '/dayReport',
  name: 'DayReport',
  component: () => import('/src/views/LeaderReport/dayReport.vue'),
  meta: {
    title: t('日报'),
    ignoreAuth: true,
    carryParam: true,
  },
};
export const dayReportRouteMobile: AppRouteRecordRaw = {
  path: '/dayReportMobile',
  name: 'DayReportMobile',
  component: () => import('/src/views/LeaderReport/dayReportMobile.vue'),
  meta: {
    title: t('日报'),
    ignoreAuth: true,
    carryParam: true,
  },
};
export const newReport: AppRouteRecordRaw = {
  path: '/newReport',
  name: 'NewReport',
  component: () => import('/src/views/LeaderReport/newReport.vue'),
  meta: {
    title: t('填报'),
    ignoreAuth: true,
    carryParam: true,
  },
};
// Basic routing without permissions
// 未经许可的基本路由
export const basicRoutes = [
  LoginRoute,
  QuestionnaireRoute,
  RootRoute,
  ...mainOutRoutes,
  REDIRECT_ROUTE,
  PAGE_NOT_FOUND_ROUTE,
  SYSTEM_ROUTE,
  USERCENTER_ROUTE,
  dayReportRoute,
  dayReportRouteMobile,
  newReport,
  // IM_ROUTE,
  // CUSTOMFORM_ROUTE,
];
