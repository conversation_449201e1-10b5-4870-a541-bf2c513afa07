import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';
import { StampType, StampFileTypeAttributes } from '/@/enums/workflowEnum';

/**
 * @description: 流程设计分页 模型
 */
export type WorkflowPageParams = BasicPageParams;

/**
 * @description: Workflow分页返回值模型
 */
export interface WorkflowPageModel {
  id: string;

  code: string;

  name: string;

  category: string;

  categoryName: string;

  appStart: number;

  definitionKey: string;
}

/**
 * @description: Workflow表类型
 */
export interface WorkflowModel {
  id: string;

  code: string;

  name: string;

  category: string;

  categoryName: string;

  appStart: number;

  content: string;

  jsonContent: string;
  xmlContent: string;
}
/**
 * @description: history表类型
 */
export interface HistoryModel {
  id: string;
  activityFlag: number;
  createDate: string;
  createUserName: string;
  version: number;
  xmlContent: string;
}

/**
 * @description: 变更 返回值
 */
export interface ChangeResultModel {
  name: string;
  detail: string;
  serailNumber: number;
  statue: number;
}
/**
 * @description: Workflow分页返回值结构
 */
export type WorkflowPageResult = BasicFetchResult<WorkflowPageModel>;

/**
 * @description: 流程列表
 */
export interface TasksModel {
  [x: string]: any;
  taskId: string;
  processId: string;
  serialNumber: string;

  schemaName: string;

  schemaId: string;

  currentTaskName: string;

  currentSchedule: string | null;
  createTime: string;
}

/**
 * @description: 流程任务返回值结构
 */

export interface TaskResult {
  pageSize: number;
  total: number;
  list: Array<TasksModel>;
}
// FinishedTask
export interface FinishedTask {
  currentNodes: Array<string>;
  finishedNodes: Array<string>;
}

export interface ApproveUserItem {
  gender: null;
  id: string;
  mobile: string;
  name: string;
  userName: string;
  canRemove: boolean;
}

/**
 * @description: 流程监控列表
 */
export interface ProcessMonitorModel {
  status: string;
  taskId: string;
  processId: string;
  serialNumber: string;

  schemaName: string;

  schemaId: string;

  currentTaskName: string;

  currentSchedule: string | null;
  createTime: string;
}

/**
 * @description: 签章
 */
export interface StampInfo {
  enabledMark: number;
  fileType: StampFileTypeAttributes;
  fileUrl: string;
  id: string;
  password: string;
  name: string;
  stampType: StampType;
  stampCategory?: string;
  remark: string;
  sortCode?: number;
  maintain: string;
  isDefault?: number;
}
/**
 * @description: 获取可以变更节点参数
 */
export interface AllNodesParams {
  processId: string;
  taskId: string;
}

/**
 * @description: 获取可以变更节点返回
 */
export interface AllNodesModal {
  activityId: string;
  activityName: string;
}

/**
 * @description: 变更节点参数
 */
export interface ChangeNodesParams {
  processId: string;
  taskId: string;
  activityId: string;
}

/**
 * @description: 签章分页返回值结构
 */
export type StampPageResult = BasicFetchResult<StampInfo>;
