import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  TaskPage = '/business/taskmanagement/page',
  Task = '/business/taskmanagement/getOtcSalesmanTask',
  TaskDel = '/business/taskmanagement/delete',
  TaskAdd = '/business/taskmanagement/saveOtcSalesmanTask',
  TaskUpdate = '/business/taskmanagement/UpdateOtcSalesmanTask',
  TaskEnabledUpdate = '/business/taskmanagement/UpdateTaskIsEnabled',
  export = '/business/taskDetail/visitExport',
  visitList = '/business/taskDetail/visitList',
  visitListTotal = '/business/taskDetail/visitTotal',
  SalesPage = '/business/taskmanagement/salesPerformanceDetailPage',
  exportSalesPage = '/business/taskmanagement/exportSalesPerformanceDetail',
  performanceList ='/business/performance/list',
  exportPerformance = '/business/performance/exportSalesPerformanceDetail',
  getOtcTaskTypeUnit = '/business/taskmanagement/getOtcTaskTypeUnit'
}

export async function getPerformanceList(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.performanceList,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: Task列表(分页)
 */
export async function getTaskPage(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.TaskPage,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 根据id查询Task信息
 */
export async function getTask(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.Task,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 修改Task
 */
export async function toTaskUpdate(data: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.TaskUpdate,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 修改Task启停用状态
 */
export async function updateTaskEnabled(data: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.TaskEnabledUpdate,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增Task
 */
export async function toTaskAdd(data: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.TaskAdd,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除Task
 */
export async function toTaskDel(data: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.TaskDel,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 拜访/协访任务明细
 */
export async function visitList(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.visitList,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 拜访/协访任务统计
 */
export async function visitListTotal(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.visitListTotal,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 拜访/协访任务明细导出
 */
export async function exportVisitData(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.download(
    {
      url: Api.export,
      method: 'POST',
      data,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}
export async function exportPerformance(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.download(
    {
      url: Api.exportPerformance,
      method: 'POST',
      data,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 销售业绩达成明细
 */
export async function getSalesPage(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.SalesPage,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 销售业绩达成明细
 */
export async function exportSalesPage(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.download(
    {
      url: Api.exportSalesPage,
      method: 'GET',
      params,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 根据目标类型查询可能单位项
 */
export async function getOtcTaskTypeUnit(params?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getOtcTaskTypeUnit,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
