import { BasicFetchResult, BasicPageParams } from '/@/api/model/baseModel';

/**
 * 数据库连接模型
 */
export interface DatabaseLinkModel {
  id: string;
  host: string;
  username: string;
  password: string;
  driver: string;
  dbName: string;
  dbVersion?: string;
  dbAlisa: string;
  dbType?: string;
  remark?: string;
  sortCode?: number;
  createDate?: string;
}

/**
 * 查询参数模型
 */
export interface DatabaseLinkParams {
  dbName?: string;
  dbType?: string;
}

/**
 * @description: Request page interface parameters
 */
export type DatabaseLinkPageParamsModel = BasicPageParams & DatabaseLinkParams;

/**
 * 数据库连接所有表的模型
 */
export interface DatabaseLinkTableInfo {
  tableName?: string;
  tableComment?: string;
  pkField?: string;
}

/**
 * @description: Request list return value
 */
export type DatabaseLinkPageResultModel = BasicFetchResult<DatabaseLinkModel>;
