import type { RouteMeta } from 'vue-router';
import { MenuTag } from '/@/router/types';
export interface RouteItem {
  path: string;
  component: any;
  meta: RouteMeta;
  name?: string;
  alias?: string | string[];
  redirect?: string;
  caseSensitive?: boolean;
  children?: RouteItem[];
}

/**
 * @description: Request menu tree interface parameters
 */
export interface MenuTreeParams {
  name: string; //部门名
  code: string; //编码
}

export interface SystemMenuTreeParams {
  systemId: string; //系统id
}

export interface MenuTreeModel {
  id: string;

  name: string;

  title: string;

  icon?: string;

  path: string;

  disabled?: boolean;

  children?: MenuTreeModel[];

  sortCode?: number;

  meta?: Partial<RouteMeta>;

  tag?: MenuTag;

  hideMenu?: boolean;
}

export interface MenuModel {
  name: string;

  icon?: string;

  path: string;

  disabled?: boolean;

  sortCode?: number;

  tag?: MenuTag;

  hideMenu?: boolean;
}
