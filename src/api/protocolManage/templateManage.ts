import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  templateList = '/business/contractTemplateFiled/page',
  updateList = '/business/contractTemplateFiled/update',
  addList = '/business/contractTemplateFiled/add',
  deleteList = '/business/contractTemplateFiled/delete',
  getTemplateFields = '/business/contractFiled/list',
  enableList = '/business/contractTemplateFiled/enable',
  disableList = '/business/contractTemplateFiled/disable',
  yearList = '/business/contractTemplateFiled/years',
  infoList = '/business/contractTemplateFiled/info',
}

export async function templateList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.templateList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}

export async function addFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.addList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}

export async function updateFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.updateList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}

export async function deleteFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.deleteList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}

export async function TemplateFieldList(params?: any, mode: ErrorMessageMode = 'modal') {
  // 使用您知道的实际接口URL
  return defHttp.get(
    {
      url: Api.getTemplateFields,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function enableFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.enableList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}

export async function disableFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.disableList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}

export async function yearFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.yearList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}

export async function infoFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.infoList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}
