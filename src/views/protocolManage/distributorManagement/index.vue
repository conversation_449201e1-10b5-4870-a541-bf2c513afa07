<template>
  <div class="fieldManage">
    <div class="page-container">
      <div class="explore">
        <a-input
          style="width: 240px"
          v-model:value="searchForm.relativeName"
          placeholder="请输入相对方名称"
        />
        <a-select
          style="width: 240px"
          class="select-color"
          v-model:value="searchForm.syncStatus"
          placeholder="请选择合同系统状态"
          allow-clear
        >
          <a-select-option value="1">未同步</a-select-option>
          <a-select-option value="2">已同步</a-select-option>
          <a-select-option value="3">同步失败</a-select-option>
        </a-select>
        <div class="button-style">
          <a-button type="primary" @click="getList()">搜索</a-button>
          <a-button @click="reSet()">重置</a-button>
          <a-button type="primary" @click="handleAdd()">新增</a-button>
        </div>
      </div>
      <div class="table_box">
        <c-table
          :tableColumns="tableColumns"
          :tableData="tableData.data"
          :loading="loading"
          :currentPage="pagination.currentPage"
          :totalItems="pagination.totalItems"
          :pageSize="pagination.pageSize"
          @update:current-page="(value) => (pagination.currentPage = value)"
          @pagination-change="handlePaginationChange"
        >
          <template #syncStatus="{ record }">
            <span>
              {{
                record.syncStatus === '1'
                  ? '未同步'
                  : record.syncStatus === '2'
                  ? '同步'
                  : record.syncStatus === '3'
                  ? '同步失败'
                  : record.type
              }}
            </span>
          </template>
          <template #action="{ record }">
            <a-button-group>
              <a-button type="link" size="small" @click="handleDetail(record)">查看</a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
              <a-button type="link" size="small" @click="handleDelete(record)">删除</a-button>
            </a-button-group>
          </template>
        </c-table>
      </div>
    </div>

    <a-modal
      v-if="visible"
      :visible="true"
      :title="modalTitle"
      @ok="handleOk"
      @cancel="handleCancel"
      wrap-class-name="full-modal"
      :width="modalWidth"
      :footer="isDetail ? null : undefined"
      :body-style="modalBodyStyle"
    >
      <a-form
        :model="formState"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
        :rules="formRules"
        ref="formRef"
      >
        <div style="margin-top: 16px">
          <a-form-item label="相对方分类" name="fieldRelativeType">
            <a-select
              v-model:value="formState.fieldRelativeType"
              placeholder="请选择相对方分类"
              :disabled="isDetail"
            >
              <a-select-option value="其它">其它</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="相对方名称" name="fieldRelativeName">
            <a-input v-model:value="formState.fieldRelativeName" placeholder="请输入" :disabled="isDetail"/>
          </a-form-item>
          <a-form-item label="相对方类别" name="fieldRelativeCategory">
            <a-select
              v-model:value="formState.fieldRelativeCategory"
              placeholder="请选择相对方类别"
              :disabled="isDetail"
            >
              <a-select-option value="企业">企业</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="相对方来源" name="fieldRelativeResource">
            <a-select
              v-model:value="formState.fieldRelativeResource"
              placeholder="请选择相对方来源"
              :disabled="isDetail"
            >
              <a-select-option value="外部">外部</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="证件类型" name="fieldCertificateType">
            <a-select
              v-model:value="formState.fieldCertificateType"
              placeholder="请选择证件类型"
              :disabled="isDetail"
            >
              <a-select-option value="企业统一社会信用代码">企业统一社会信用代码</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="证件号码">
            <a-input
              v-model:value="formState.fieldCertificateNo"
              placeholder="请输入"
              :disabled="isDetail"
            />
          </a-form-item>
          <a-form-item label="开户行">
            <a-input
              v-model:value="formState.fieldNewBank"
              placeholder="请输入"
              :disabled="isDetail"
            />
          </a-form-item>
          <a-form-item label="银行账户名">
            <a-input
              v-model:value="formState.fieldNewBankAccountName"
              placeholder="请输入"
              :disabled="isDetail"
            />
          </a-form-item>
          <a-form-item label="银行账号">
            <a-input
              v-model:value="formState.fieldNewBankAccount"
              placeholder="请输入"
              :disabled="isDetail"
            />
          </a-form-item>
          <a-form-item label="签约联系人">
            <a-input
              v-model:value="formState.fieldContractLinkmanName"
              placeholder="请输入"
              :disabled="isDetail"
            />
          </a-form-item>
          <a-form-item label="签约联系人联系方式">
            <a-input
              v-model:value="formState.fieldContractLinkmanPhone"
              placeholder="请输入"
              :disabled="isDetail"
            />
          </a-form-item>
          <a-form-item label="备注">
            <a-input
              v-model:value="formState.fieldRemark"
              placeholder="请输入"
              :disabled="isDetail"
            />
          </a-form-item>
        </div>
      </a-form>
      <div v-if="isDetail" class="detail-footer">
        <a-button @click="handleCancel">关闭</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, onMounted, type UnwrapRef, computed } from 'vue';
  import cTable from '/@/views/components/Table/index.vue';
  import {
    getContractList,
    addFieldList,
    updateFieldList,
    infoFieldList,
    deleteFieldList,
  } from '/@/api/protocolManage/distributorManagement';
  import { FormInstance, message } from 'ant-design-vue';
  const loading = ref(false);
  const currentEditIndex = ref(-1);
  const visible = ref(false);
  const labelCol = { span: 6 };
  const wrapperCol = { span: 16 };
  const isDetail = ref(false);
  const formRef = ref<FormInstance>();
  const modalType = ref('add');
  const modalTitle = computed(() => {
    if (modalType.value === 'add') return '新增';
    if (modalType.value === 'edit') return '编辑';
    return '详情';
  });
  const modalBodyStyle = {
    maxHeight: '70vh',
    overflowY: 'auto',
  };
  const modalWidth = computed(() => {
    if (typeof window !== 'undefined') {
      const screenWidth = window.innerWidth;
      if (screenWidth < 576) {
        return '95%';
      } else if (screenWidth < 768) {
        return '90%';
      } else if (screenWidth < 992) {
        return '80%';
      } else if (screenWidth < 1200) {
        return '70%';
      } else {
        return '60%';
      }
    }
    return '800px'; // 默认值
  });
  const formRules = reactive({
    fieldRelativeName: [{ required: true, message: '请输入相对方名称', trigger: 'blur' }],
    fieldRelativeCategory: [{ required: true, message: '请输入相对方类别', trigger: 'blur' }],
    fieldRelativeType: [{ required: true, message: '请输入相对方分类', trigger: 'blur' }],
    fieldRelativeResource: [{ required: true, message: '请输入相对方来源', trigger: 'blur' }],
  });
  onMounted(() => {
    getList();
  });
  const searchForm = reactive({
    certificateNo: '',
    certificateType: '',
    contractLinkmanName: '',
    contractLinkmanPhone: '',
    newBank: '',
    newBankAccount: '',
    newBankAccountName: '',
    relativeCategory: '',
    relativeName: '',
    relativeResource: '',
    relativeType: '',
    remark: '',
    syncStatus: undefined,
    id: '',
  });
  interface FormState {
    fieldCertificateNo: string;
    fieldCertificateType: string;
    fieldContractLinkmanName: string;
    fieldContractLinkmanPhone: string;
    fieldNewBank: string;
    fieldNewBankAccount: string;
    fieldNewBankAccountName: string;
    fieldRelativeCategory: string;
    fieldRelativeName: string;
    fieldRelativeResource: string;
    fieldRelativeType: string;
    fieldRemark: string;
    fieldSyncStatus: undefined;
    id: string;
  }

  const formState: UnwrapRef<FormState> = reactive({
    fieldName: '',
    fieldCertificateNo: '',
    fieldCertificateType: '',
    fieldContractLinkmanName: '',
    fieldContractLinkmanPhone: '',
    fieldNewBank: '',
    fieldNewBankAccount: '',
    fieldNewBankAccountName: '',
    fieldRelativeCategory: '',
    fieldRelativeName: '',
    fieldRelativeResource: '',
    fieldRelativeType: '',
    fieldRemark: '',
    fieldSyncStatus: undefined,
    id: '',
  });
  const pagination = reactive({
    currentPage: 1,
    totalItems: 500,
    pageSize: 10,
  });
  // 表格列配置
  const tableColumns = ref([
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center', width: 80 },
    { title: '相对方分类', dataIndex: 'relativeType', key: 'relativeType', align: 'center' },
    { title: '相对方名称', dataIndex: 'relativeName', key: 'relativeName', align: 'center' },
    {
      title: '相对方类别',
      dataIndex: 'relativeCategory',
      key: 'relativeCategory',
      align: 'center',
    },
    {
      title: '相对方来源',
      dataIndex: 'relativeResource',
      key: 'relativeResource',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      width: 180,
      isSlot: true,
      slotName: 'action',
    },
  ]);
  const tableData = reactive({
    data: [],
  });
  const reSet = () => {
    searchForm.certificateNo = '';
    searchForm.certificateType = '';
    searchForm.contractLinkmanName = '';
    searchForm.contractLinkmanPhone = '';
    searchForm.newBank = '';
    searchForm.newBankAccount = '';
    searchForm.newBankAccountName = '';
    searchForm.relativeCategory = '';
    searchForm.relativeName = '';
    searchForm.relativeResource = '';
    searchForm.relativeType = '';
    searchForm.remark = '';
    searchForm.syncStatus = undefined;
    getList();
  };

  const getList = async (flag?: number) => {
    if (!flag) {
      pagination.currentPage = 1;
    }

    loading.value = true;
    tableData.data = [];

    try {
      const params = {
        limit: pagination.currentPage,
        size: pagination.pageSize,
        certificateNo: searchForm.certificateNo || undefined,
        certificateType: searchForm.certificateType || undefined,
        contractLinkmanName: searchForm.contractLinkmanName || undefined,
        contractLinkmanPhone: searchForm.contractLinkmanPhone || undefined,
        newBank: searchForm.newBank || undefined,
        newBankAccount: searchForm.newBankAccount || undefined,
        newBankAccountName: searchForm.newBankAccountName || undefined,
        relativeCategory: searchForm.relativeCategory || undefined,
        relativeName: searchForm.relativeName || undefined,
        relativeResource: searchForm.relativeResource || undefined,
        relativeType: searchForm.relativeType || undefined,
        remark: searchForm.remark || undefined,
        syncStatus: searchForm.syncStatus || undefined,
      };

      // 调用API获取数据
      const res = await getContractList(params);

      // 更新表格数据和分页信息
      if (res && res.list) {
        tableData.data = res.list.map((item, index) => ({
          ...item,
          index: (pagination.currentPage - 1) * pagination.pageSize + index + 1,
          required: item.required === '1',
        }));
        pagination.totalItems = res.total || 0;
      }

      loading.value = false;
    } catch (error) {
      loading.value = false;
    }
  };
  // 事件处理函数
  const handlePaginationChange = (page: any) => {
    pagination.currentPage = page.current;
    pagination.pageSize = page.pageSize;
    getList(1);
  };

  const handleAdd = () => {
    modalType.value = 'add';
    console.log('新增记录');
    resetForm();
    currentEditIndex.value = -1;
    visible.value = true;
    isDetail.value = false;
    // 清除验证状态
    setTimeout(() => {
      formRef.value?.clearValidate();
    }, 0);
  };
  const handleOk = async () => {
    if (isDetail.value) {
      visible.value = false;
      return;
    }
    if (formRef.value) {
      try {
        // 表单验证
        await formRef.value.validate();

        // 验证通过后，直接执行提交逻辑
        loading.value = true;
        const params = {
          relativeType: formState.fieldRelativeType,
          relativeName: formState.fieldRelativeName,
          relativeCategory: formState.fieldRelativeCategory,
          relativeResource: formState.fieldRelativeResource,
          certificateType: formState.fieldCertificateType,
          certificateNo: formState.fieldCertificateNo,
          contractLinkmanName: formState.fieldContractLinkmanName,
          contractLinkmanPhone: formState.fieldContractLinkmanPhone,
          newBank: formState.fieldNewBank,
          newBankAccount: formState.fieldNewBankAccount,
          newBankAccountName: formState.fieldNewBankAccountName,
          remark: formState.fieldRemark,
          syncStatus: formState.fieldSyncStatus,
          id: formState.id,
        };

        if (formState.id) {
          params.id = formState.id;
          await updateFieldList(params);
          message.success('编辑成功');
        } else {
          await addFieldList(params);
          message.success('新增成功');
        }

        visible.value = false;
        resetForm();
        getList();
      } catch (error) {
        console.error('保存失败:', error);
      } finally {
        loading.value = false;
      }
    }
  };
  const fillFormState = (record: any) => {
    // 填充表单状态
    formState.fieldRelativeType = record.relativeType;
    formState.fieldRelativeName = record.relativeName;
    formState.fieldRelativeCategory = record.relativeCategory;
    formState.fieldRelativeResource = record.relativeResource;
    formState.fieldCertificateType = record.certificateType;
    formState.fieldCertificateNo = record.certificateNo;
    formState.fieldContractLinkmanName = record.contractLinkmanName;
    formState.fieldContractLinkmanPhone = record.contractLinkmanPhone;
    formState.fieldNewBank = record.newBank;
    formState.fieldNewBankAccount = record.newBankAccount;
    formState.fieldNewBankAccountName = record.newBankAccountName;
    formState.fieldRemark = record.remark;
    formState.fieldSyncStatus = record.syncStatus;
    formState.id = record.id;
  };
  const handleDetail = async (record: any) => {
    modalType.value = 'detail';
    console.log('查看详情:', record);
    try {
      loading.value = true;
      // 调用获取详情的API
      const detailRes = await infoFieldList({ id: record.id });

      if (detailRes) {
        // 填充表单状态
        fillFormState(detailRes);
        visible.value = true;
        isDetail.value = true;
      } else {
        message.error('获取详情失败');
      }
    } catch (error) {
      console.error('获取详情失败:', error);
      message.error('获取详情失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  };

  const resetForm = () => {
    formState.fieldCertificateNo = '';
    formState.fieldCertificateType = '';
    formState.fieldContractLinkmanName = '';
    formState.fieldContractLinkmanPhone = '';
    formState.fieldNewBank = '';
    formState.fieldNewBankAccount = '';
    formState.fieldNewBankAccountName = '';
    formState.fieldRelativeCategory = '';
    formState.fieldRelativeName = '';
    formState.fieldRelativeResource = '';
    formState.fieldRelativeType = '';
    formState.fieldRemark = '';
    formState.fieldSyncStatus = undefined;
    formState.id = '';
  };
  const handleEdit = (record: any) => {
    modalType.value = 'edit';
    console.log('编辑记录:', record);
    currentEditIndex.value = record.id;
    fillFormState(record);
    visible.value = true;
    isDetail.value = false;
  };
  const handleDelete = (record: any) => {
    console.log('删除记录:', record);
    deleteFieldList({ id: record.id }).then((_) => {
      getList();
      message.success('删除成功');
    });
  };
  const handleCancel = () => {
    visible.value = false;
    resetForm();
  };
</script>
<style scoped lang="less">
  .fieldManage {
    width: 100%;
    height: 100%;
    padding: 8px;
    background-color: white;
    :deep(.explore) {
      display: flex;
      font-size: 16px;
      gap: 16px;
      background-color: #fff;
      margin-bottom: 0;
      padding: 16px 0 0 16px;
      align-items: center;
    }

    .label-text {
      white-space: nowrap;
    }

    .button-style {
      > * + * {
        margin-left: 16px;
      }
    }
  }
  .table_box {
    padding: 16px;
  }
  .detail-footer {
    text-align: right;
    padding: 16px;
  }
</style>
