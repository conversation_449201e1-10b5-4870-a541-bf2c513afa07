<template>
  <PageWrapper dense fixed-height contentFullHeight>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate" v-auth="'report-release:add'">
          <template #icon><plus-outlined /></template>
          {{ t('新增') }}
        </a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'clarity:note-edit-line',
              auth: 'report-release:edit',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ant-design:delete-outlined',
              auth: 'report-release:delete',
              color: 'error',
              custom: true,
              onClick: handleDel.bind(null, record),
              popConfirm: {
                visible: curID == record.id,
                confirm: handleDelete.bind(null, record),
                deleteMenu: handleDeleteMenu.bind(null, record),
                cancel: () => {
                  curID = '';
                },
              },
            },
          ]"
        />
      </template>
    </BasicTable>
    <ReportModal @register="registerModal" @success="reload" />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, FormSchema, BasicColumn } from '/@/components/Table';
  import {
    getReportReleasePage,
    deleteReportRelease,
    deleteReportAndMenu,
  } from '/@/api/system/report';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useModal } from '/@/components/Modal';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import ReportModal from './components/ReportModal.vue';
  import { PageWrapper } from '/@/components/Page';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { ref } from 'vue';

  const { t } = useI18n();
  const columns: BasicColumn[] = [
    {
      title: () => t('菜单编号'),
      dataIndex: 'code',
      align: 'left',
      resizable: true,
    },
    {
      title: () => t('菜单名称'),
      dataIndex: 'title',
      align: 'left',
      resizable: true,
    },
    {
      title: () => t('父级菜单'),
      dataIndex: 'parentName',
      align: 'left',
      resizable: true,
    },
    {
      title: () => t('绑定报表'),
      dataIndex: 'reportName',
      align: 'left',
      resizable: true,
    },
    {
      title: () => t('备注'),
      dataIndex: 'remark',
      align: 'left',
      resizable: true,
    },
  ];

  const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: () => t('关键字'),
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: t('请输入关键字'),
      },
    },
  ];

  const { notification } = useMessage();
  const { hasPermission } = usePermission();
  const curID = ref('');
  const [registerModal, { openModal }] = useModal();

  const [registerTable, { reload }] = useTable({
    title: () => t('报表列表'),
    api: getReportReleasePage,
    columns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      showResetButton: false,
    },
    striped: false,
    useSearchForm: true,
    showTableSetting: true,
    actionColumn: {
      width: 80,
      title: () => t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: undefined,
    },
    tableSetting: {
      size: false,
    },
    customRow: (record) => {
      return {
        ondblclick: () => {
          if (hasPermission('report-release:edit')) {
            handleEdit(record);
          }
        },
      };
    },
  });

  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    openModal(true, {
      isUpdate: true,
      id: record.id,
    });
  }

  function handleDelete(record: Recordable) {
    deleteReportRelease([record.id]).then((_) => {
      reload();
      notification.success({
        message: t('提示'),
        description: t('删除成功！'),
      });
    });
  }
  function handleDeleteMenu(record: Recordable) {
    deleteReportAndMenu([record.id]).then((_) => {
      reload();
      notification.success({
        message: t('提示'),
        description: t('删除成功'),
      }); //提示消息
    });
  }
  function handleDel(record: Recordable) {
    curID.value = record.id;
  }
</script>
