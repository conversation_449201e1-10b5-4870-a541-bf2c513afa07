<template>
  <a-row :gutter="14" class="!mx-0">
    <template v-for="item in growCardList" :key="item.title">
      <a-col :span="6">
        <Card size="small" class="w-full !mb-14px">
          <img :src="item.image" width="140" />
          <p class="card-name">{{ item.name }}</p>
          <p class="card-content">{{ item.content }}</p>
          <ImportData v-if="item.import">
            <a-button type="primary">{{ item.btnName }}</a-button>
          </ImportData>
          <a-button v-if="!item.import" type="primary" @click="item.func(item.type)">{{
            item.btnName
          }}</a-button>
        </Card>
      </a-col>
    </template>
  </a-row>
</template>
<script lang="ts" setup>
  import { Card } from 'ant-design-vue';
  import ImportData from './ImportData.vue';
  defineProps({
    growCardList: {
      type: [Array] as PropType<any[]>,
      required: true,
    },
  });
</script>
<style lang="less" scoped>
  :deep(.ant-card:last-child) {
    margin-right: 0 !important;
  }

  :deep(.ant-card-body) {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 10px 40px;

    .card-name {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 10px;
    }

    .card-content {
      color: #999;
      margin-bottom: 20px;
    }
  }

  :deep(.ant-btn) {
    width: 130px;
    height: 40px;
    background: #5e95ff;
    box-shadow: 0 4px 18px 0 rgb(94 149 255 / 50%);
    border-radius: 0 30px 30px;
    border-color: transparent;
  }
</style>
