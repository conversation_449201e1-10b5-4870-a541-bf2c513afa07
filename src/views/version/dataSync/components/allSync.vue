<template>
  <GroupCard :growCardList="items" />
</template>
<script lang="ts" setup>
  import GroupCard from './GroupCard.vue';
  import dicImportPng from '/@/assets/sync/dicImport.png';
  import linkImportPng from '/@/assets/sync/linkImport.png';
  import menuImportPng from '/@/assets/sync/menuImport.png';
  import codeImportPng from '/@/assets/sync/codeImport.png';
  import dicExportPng from '/@/assets/sync/dicExport.png';
  import linkExportPng from '/@/assets/sync/linkExport.png';
  import menuExportPng from '/@/assets/sync/menuExport.png';
  import codeExportPng from '/@/assets/sync/codeExport.png';
  import { exportone } from '/@/api/version/dataSync';
  import { downloadByData } from '/@/utils/file/download';
  import { useMessage } from '/@/hooks/web/useMessage';
  const { notification } = useMessage();
  const items = [
    {
      name: '功能菜单导入',
      type: 'menu',
      content: '将文件拖到此处，或点击按钮上传',
      btnName: '立即导入',
      import: true,
      image: menuImportPng,
    },
    {
      name: '功能菜单导出',
      type: 'menu',
      content: '点击下方按钮导出功能菜单文件',
      btnName: '立即导出',
      import: false,
      image: menuExportPng,
      func: handleSubmit,
    },
    {
      name: '数据字典导入',
      type: 'dic',
      content: '将文件拖到此处，或点击按钮上传',
      btnName: '立即导入',
      import: true,
      image: dicImportPng,
    },
    {
      name: '数据字典导出',
      type: 'dic',
      content: '点击下方按钮导出数据字典文件',
      btnName: '立即导出',
      import: false,
      image: dicExportPng,
      func: handleSubmit,
    },
    {
      name: '数据连接导入',
      type: 'link',
      content: '将文件拖到此处，或点击按钮上传',
      btnName: '立即导入',
      import: true,
      image: linkImportPng,
    },
    {
      name: '数据连接导出',
      type: 'link',
      content: '点击下方按钮导出数据连接文件',
      btnName: '立即导出',
      import: false,
      image: linkExportPng,
      func: handleSubmit,
    },
    {
      name: '单据编码导入',
      type: 'billcode',
      content: '将文件拖到此处，或点击按钮上传',
      btnName: '立即导入',
      import: true,
      image: codeImportPng,
    },
    {
      name: '单据编码导出',
      type: 'billcode',
      content: '点击下方按钮导出单据编码文件',
      btnName: '立即导出',
      import: false,
      image: codeExportPng,
      func: handleSubmit,
    },
  ];
  async function handleSubmit(type) {
    try {
      let res: any = await exportone(type);
      downloadByData(res, '系统基础数据.json');
      notification.success({
        message: '提示',
        description: '导出成功',
      });
    } catch (error) {
      notification.error({
        message: '提示',
        description: '导出失败',
      });
    }
  }
</script>
