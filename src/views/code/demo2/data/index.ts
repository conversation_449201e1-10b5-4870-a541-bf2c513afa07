import { Input } from 'ant-design-vue';
import { h } from 'vue';
import { getPostList } from '/@/api/system/post';
import { FormProps, FormSchema } from '/@/components/Form';
import { debounce, sum, multiply } from 'lodash-es';
import { uploadApi } from '/@/api/sys/upload';

export const getFormConfig: FormSchema[] = [
  {
    field: 'fieldString',
    label: '字符串',
    component: 'Input',
    rules: [{ required: true, message: 'Please input your fieldString!', trigger: 'blur' }],
    colProps: { span: 24 },
    componentProps: ({ formModel, formActionType }) => {
      return {
        placeholder: '这里可以设置很多东西',
        //这里如果需要修改其他的组件 最好加上防抖函数 优化性能
        onChange: debounce((e: ChangeEvent) => {
          //fieldInt组件会根据fieldString的组件值 变化 +2
          formModel.fieldInt = e.target.value + '整型';
          //fieldString组件值变化 修改postId组件的params  ApiSelect组件会重新发起请求
          formActionType.updateSchema({
            field: 'postId',
            componentProps: {
              params: { keyword: e.target.value },
            },
          });
        }, 1000),
      };
    },
  },
  {
    field: 'childUseMain',
    label: '子表调用主表计算',
    component: 'InputNumber',
    rules: [{ required: true, message: 'Please input your childUseMain!', trigger: 'blur' }],
    colProps: { span: 24 },
    componentProps: ({ formModel }) => {
      return {
        placeholder: '这里可以设置很多东西',
        //这里如果需要修改其他的组件 最好加上防抖函数 优化性能
        onChange: debounce((e) => {
          console.log('formModel', formModel, e);
          formModel.subform.forEach((item) => {
            item['fieldLong'] = multiply(e, item['fieldDouble']);
          });
        }, 1000),
      };
    },
  },
  {
    field: 'mainUseChild',
    label: '主表使用子表计算',
    component: 'Input',
    rules: [{ required: true, message: 'Please input your mainUseChild!', trigger: 'blur' }],
    colProps: { span: 24 },
  },
  {
    field: 'postId',
    label: '所属岗位',
    component: 'ApiSelect',
    componentProps: {
      params: { itemId: '1419276800524423168' },
      resultField: 'data',
      labelField: 'name',
      valueField: 'id',
      datasourceType: 'dic',
      allowClear: true,
      // api: getPostList,
      // // use name as label
      // labelField: 'name',
      // // use id as value
      // valueField: 'id',
      getPopupContainer: () => document.body,
    },
    required: true,
    colProps: { span: 24 },
  },
  {
    field: 'fieldSwitch',
    label: '控制整型',
    component: 'Switch',
    colProps: { span: 8 },
  },
  {
    field: 'fieldInt',
    label: '整型',
    rules: [{ required: true, message: 'Please input your fieldInt!', trigger: 'blur' }],
    component: 'Input',
    colProps: { span: 8 },
    show: ({ values, model, schema }) => {
      //显示  就必填 不显示就不必填
      schema!.rules![0].required = !!model.fieldSwitch;
      console.log('values', values, model);
      return model.fieldSwitch;
    },
  },
  {
    field: 'fieldDatetimeStartTime,fieldDatetimeEndTime',
    label: '时间',
    rules: [{ required: true, message: 'Please input your fieldDatetime!', trigger: 'blur' }],
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
    },
    colProps: { span: 8 },
  },
  {
    field: 'fieldDouble',
    label: '浮点',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'fieldLong',
    label: '长整数',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'fieldUpload',
    component: 'Upload',
    label: '上传',
    colProps: {
      span: 8,
    },
    rules: [{ required: true, message: 'Please input your fieldUpload!', trigger: 'blur' }],
    componentProps: {
      api: uploadApi,
    },
  },
  {
    field: '',
    label: '',
    span: 24,
    colProps: { span: 24 },
    component: 'Tab',
    children: [
      {
        span: 24,
        name: 'tab1',
        list: [
          {
            label: '单行文本',
            component: 'Input',
            colProps: {
              span: 24,
            },
            field: 'tab111',
            rules: [
              {
                trigger: 'blur',
                enum: '',
                message: '',
                pattern: '',
                required: false,
                type: 'any',
              },
            ],
            required: false,
            defaultValue: '',
            componentProps: {
              width: '100%',
              defaultValue: '',
              placeholder: '',
              maxlength: null,
              prefix: '',
              suffix: '',
              addonBefore: '',
              addonAfter: '',
              allowClear: false,
              readonly: false,
              rules: {
                trigger: 'blur',
                enum: '',
                message: '',
                pattern: '',
                required: false,
                type: 'any',
              },
            },
          },
          {
            label: '单行文本',
            component: 'Input',
            colProps: {
              span: 24,
            },
            field: 'tab222',
            rules: [
              {
                trigger: 'blur',
                enum: '',
                message: '',
                pattern: '',
                required: false,
                type: 'any',
              },
            ],
            required: false,
            defaultValue: '',
            componentProps: {
              width: '100%',
              defaultValue: '',
              placeholder: '',
              maxlength: null,
              prefix: '',
              suffix: '',
              addonBefore: '',
              addonAfter: '',
              allowClear: false,
              readonly: false,
              rules: {
                trigger: 'blur',
                enum: '',
                message: '',
                pattern: '',
                required: false,
                type: 'any',
              },
            },
          },
        ],
      },
      {
        span: 24,
        name: 'tab2',
        list: [
          {
            field: '',
            label: '',
            span: 24,
            colProps: { span: 24 },
            component: 'Tab',
            children: [
              {
                span: 24,
                name: 'tab1',
                list: [
                  {
                    label: '单行文本',
                    component: 'Input',
                    colProps: {
                      span: 24,
                    },
                    field: 'tab111',
                    rules: [
                      {
                        trigger: 'blur',
                        enum: '',
                        message: '',
                        pattern: '',
                        required: false,
                        type: 'any',
                      },
                    ],
                    required: false,
                    defaultValue: '',
                    componentProps: {
                      width: '100%',
                      defaultValue: '',
                      placeholder: '',
                      maxlength: null,
                      prefix: '',
                      suffix: '',
                      addonBefore: '',
                      addonAfter: '',
                      allowClear: false,
                      readonly: false,
                      rules: {
                        trigger: 'blur',
                        enum: '',
                        message: '',
                        pattern: '',
                        required: false,
                        type: 'any',
                      },
                    },
                  },
                  {
                    label: '单行文本',
                    component: 'Input',
                    colProps: {
                      span: 24,
                    },
                    field: 'tab222',
                    rules: [
                      {
                        trigger: 'blur',
                        enum: '',
                        message: '',
                        pattern: '',
                        required: false,
                        type: 'any',
                      },
                    ],
                    required: false,
                    defaultValue: '',
                    componentProps: {
                      width: '100%',
                      defaultValue: '',
                      placeholder: '',
                      maxlength: null,
                      prefix: '',
                      suffix: '',
                      addonBefore: '',
                      addonAfter: '',
                      allowClear: false,
                      readonly: false,
                      rules: {
                        trigger: 'blur',
                        enum: '',
                        message: '',
                        pattern: '',
                        required: false,
                        type: 'any',
                      },
                    },
                  },
                ],
              },
              {
                span: 24,
                name: 'tab2',
                list: [
                  {
                    label: '单行文本',
                    component: 'Input',
                    colProps: {
                      span: 24,
                    },
                    field: 'tab333',
                    rules: [
                      {
                        trigger: 'blur',
                        enum: '',
                        message: '',
                        pattern: '',
                        required: false,
                        type: 'any',
                      },
                    ],
                    required: false,
                    defaultValue: '',
                    componentProps: {
                      width: '100%',
                      defaultValue: '',
                      placeholder: '',
                      maxlength: null,
                      prefix: '',
                      suffix: '',
                      addonBefore: '',
                      addonAfter: '',
                      allowClear: false,
                      readonly: false,
                      rules: {
                        trigger: 'blur',
                        enum: '',
                        message: '',
                        pattern: '',
                        required: false,
                        type: 'any',
                      },
                    },
                  },
                ],
              },
              {
                span: 24,
                name: 'tab3',
                list: [
                  {
                    field: '',
                    label: '',
                    colProps: { span: 24 },
                    component: 'Grid',
                    children: [
                      {
                        span: 12,
                        list: [
                          {
                            label: '单行文本',
                            component: 'Input',
                            field: 'grid111',
                            colProps: {
                              span: 24,
                            },
                            rules: [
                              {
                                required: true,
                                message: 'Please input your fieldString!',
                                trigger: 'blur',
                              },
                            ],
                            required: false,
                            defaultValue: '',
                            componentProps: {
                              width: '100%',
                              defaultValue: '',
                              placeholder: '',
                              maxlength: null,
                              prefix: '',
                              suffix: '',
                              addonBefore: '',
                              addonAfter: '',
                              allowClear: false,
                              readonly: false,
                              rules: {
                                trigger: 'blur',
                                enum: '',
                                message: '',
                                pattern: '',
                                required: false,
                                type: 'any',
                              },
                            },
                          },
                        ],
                      },
                      {
                        span: 12,
                        list: [
                          {
                            label: '单行文本',
                            component: 'Input',
                            colProps: {
                              span: 24,
                            },
                            field: 'grid222',
                            defaultValue: '',
                            componentProps: {
                              width: '100%',
                              defaultValue: '',
                              placeholder: '',
                              maxlength: null,
                              prefix: '',
                              suffix: '',
                              addonBefore: '',
                              addonAfter: '',
                              allowClear: false,
                              readonly: false,
                              rules: {
                                trigger: 'blur',
                                enum: '',
                                message: '',
                                pattern: '',
                                required: false,
                                type: 'any',
                              },
                            },
                          },
                          {
                            label: '单行文本',
                            component: 'Input',
                            colProps: {
                              span: 24,
                            },
                            field: 'grid333',

                            required: false,
                            defaultValue: '',
                            componentProps: {
                              width: '100%',
                              defaultValue: '',
                              placeholder: '',
                              maxlength: null,
                              prefix: '',
                              suffix: '',
                              addonBefore: '',
                              addonAfter: '',
                              allowClear: false,
                              readonly: false,
                              rules: {
                                trigger: 'blur',
                                enum: '',
                                message: '',
                                pattern: '',
                                required: false,
                                type: 'any',
                              },
                            },
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
            componentProps: {
              tabPosition: 'top',
              size: 'default',
            },
          },
        ],
      },
      {
        span: 24,
        name: 'tab3',
        list: [
          {
            field: '',
            label: '',
            colProps: { span: 24 },
            component: 'Grid',
            children: [
              {
                span: 12,
                list: [
                  {
                    label: '单行文本',
                    component: 'Input',
                    field: 'grid111',
                    colProps: {
                      span: 24,
                    },
                    rules: [
                      {
                        required: true,
                        message: 'Please input your fieldString!',
                        trigger: 'blur',
                      },
                    ],
                    required: false,
                    defaultValue: '',
                    componentProps: {
                      width: '100%',
                      defaultValue: '',
                      placeholder: '',
                      maxlength: null,
                      prefix: '',
                      suffix: '',
                      addonBefore: '',
                      addonAfter: '',
                      allowClear: false,
                      readonly: false,
                      rules: {
                        trigger: 'blur',
                        enum: '',
                        message: '',
                        pattern: '',
                        required: false,
                        type: 'any',
                      },
                    },
                  },
                ],
              },
              {
                span: 12,
                list: [
                  {
                    label: '单行文本',
                    component: 'Input',
                    colProps: {
                      span: 24,
                    },
                    field: 'grid222',
                    defaultValue: '',
                    componentProps: {
                      width: '100%',
                      defaultValue: '',
                      placeholder: '',
                      maxlength: null,
                      prefix: '',
                      suffix: '',
                      addonBefore: '',
                      addonAfter: '',
                      allowClear: false,
                      readonly: false,
                      rules: {
                        trigger: 'blur',
                        enum: '',
                        message: '',
                        pattern: '',
                        required: false,
                        type: 'any',
                      },
                    },
                  },
                  {
                    label: '单行文本',
                    component: 'Input',
                    colProps: {
                      span: 24,
                    },
                    field: 'grid333',

                    required: false,
                    defaultValue: '',
                    componentProps: {
                      width: '100%',
                      defaultValue: '',
                      placeholder: '',
                      maxlength: null,
                      prefix: '',
                      suffix: '',
                      addonBefore: '',
                      addonAfter: '',
                      allowClear: false,
                      readonly: false,
                      rules: {
                        trigger: 'blur',
                        enum: '',
                        message: '',
                        pattern: '',
                        required: false,
                        type: 'any',
                      },
                    },
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
    componentProps: {
      tabPosition: 'top',
      size: 'default',
    },
  },
  {
    field: '',
    label: '',
    span: 24,
    colProps: { span: 24 },
    component: 'Card',
    children: [
      {
        span: 24,
        name: 'tab1',
        list: [
          {
            field: 'postIdstatic',
            label: '静态数据',
            component: 'Select',
            componentProps: {
              defaultValue: 'Option 2',
              options: [
                {
                  label: 'Option 1',
                  value: 'Option 1',
                },
                {
                  label: 'Option 2',
                  value: 'Option 2',
                },
                {
                  label: 'Option 3',
                  value: 'Option 3',
                },
                {
                  label: 'Option 4',
                  value: 'Option 4',
                },
              ],
              getPopupContainer: () => document.body,
            },
            required: true,
            colProps: { span: 24 },
          },
          {
            label: '单行文本',
            component: 'Input',
            colProps: {
              span: 24,
            },
            field: 'card222',
            rules: [
              {
                trigger: 'blur',
                enum: '',
                message: '',
                pattern: '',
                required: false,
                type: 'any',
              },
            ],
            required: false,
            defaultValue: '',
            componentProps: {
              width: '100%',
              defaultValue: '',
              placeholder: '',
              maxlength: null,
              prefix: '',
              suffix: '',
              addonBefore: '',
              addonAfter: '',
              allowClear: false,
              readonly: false,
              rules: {
                trigger: 'blur',
                enum: '',
                message: '',
                pattern: '',
                required: false,
                type: 'any',
              },
            },
          },
        ],
      },
    ],
    componentProps: {
      title: '卡片布局案例1',
    },
  },
  {
    field: '',
    label: '',
    span: 24,
    colProps: { span: 24 },
    component: 'Card',
    children: [
      {
        span: 24,
        name: 'tab1',
        list: [
          {
            label: '单行文本',
            component: 'Input',
            colProps: {
              span: 24,
            },
            field: 'card333',
            rules: [
              {
                trigger: 'blur',
                enum: '',
                message: '',
                pattern: '',
                required: false,
                type: 'any',
              },
            ],
            required: false,
            defaultValue: '',
            componentProps: {
              width: '100%',
              defaultValue: '',
              placeholder: '',
              maxlength: null,
              prefix: '',
              suffix: '',
              addonBefore: '',
              addonAfter: '',
              allowClear: false,
              readonly: false,
              rules: {
                trigger: 'blur',
                enum: '',
                message: '',
                pattern: '',
                required: false,
                type: 'any',
              },
            },
          },
          {
            label: '单行文本',
            component: 'Input',
            colProps: {
              span: 24,
            },
            field: 'card444',
            rules: [
              {
                trigger: 'blur',
                enum: '',
                message: '',
                pattern: '',
                required: false,
                type: 'any',
              },
            ],
            required: false,
            defaultValue: '',
            componentProps: {
              width: '100%',
              defaultValue: '',
              placeholder: '',
              maxlength: null,
              prefix: '',
              suffix: '',
              addonBefore: '',
              addonAfter: '',
              allowClear: false,
              readonly: false,
              rules: {
                trigger: 'blur',
                enum: '',
                message: '',
                pattern: '',
                required: false,
                type: 'any',
              },
            },
          },
        ],
      },
    ],
    componentProps: {
      title: '卡片布局案例1',
    },
  },
  {
    label: '子表单',
    field: 'subform',
    component: 'SubForm',
    required: true,
    colProps: { span: 24 },
    componentProps: {
      mainKey: 'subform',
      columns: [
        {
          title: '字符串',
          dataIndex: 'fieldString',
          componentType: 'Input',
          defaultValue: 111,
          rules: [
            {
              required: true,
              message: '子表单组件验证案例',
            },
          ],
          componentProps: ({ updateSchema }) => {
            return {
              placeholder: '这里可以设置很多东西',
              //这里如果需要修改其他的组件 最好加上防抖函数 优化性能
              onChange: debounce((e: ChangeEvent) => {
                updateSchema({
                  title: '联动字符串2222',
                  dataIndex: 'fieldString111',
                  componentProps: {
                    params: { keyword: e.target.value },
                  },
                });
              }, 1000),
            };
          },
        },
        {
          title: '联动字符串',
          dataIndex: 'fieldString111',
          componentType: 'ApiSelect',
          componentProps: {
            api: getPostList,
            // use name as label
            labelField: 'name',
            // use id as value
            valueField: 'id',
            getPopupContainer: () => document.body,
          },
          // width: 120,
        },
        {
          title: '整型',
          dataIndex: 'fieldInt',
          componentType: 'Switch',
          rules: [
            {
              required: true,
              message: '子表单组件验证案例',
            },
          ],
          defaultValue: true,
          componentProps: {
            checked: true,
            onChange: function () {
              console.log('子表单组件事件案例');
            },
          },
          // width: 120,
        },
        {
          title: '时间',
          dataIndex: 'fieldDatetime',
          componentType: 'RangePicker',
          fieldMapToTime: [
            ['fieldDatetime', ['fieldDatetimeStart', 'fieldDatetimeEnd'], 'YYYY-MM-DD HH:mm:ss'],
          ],
          // defaultValue: ['2022-06-15', '2022-06-18'],
          componentProps: {
            placeholder: ['开始时间', '结束时间'],
          },
          // width: 120,
        },
        {
          title: '合并',
          dataIndex: 'xxxx',
          children: [
            {
              title: '开关',
              dataIndex: 'fieldSwitch',
              componentType: 'Switch',
              // width: 120,
            },
            {
              title: '浮点',
              dataIndex: 'fieldDouble',
              componentType: 'InputNumber',
              componentProps: ({ formModel, record }) => {
                return {
                  placeholder: '这里可以设置很多东西',
                  //这里如果需要修改其他的组件 最好加上防抖函数 优化性能
                  onChange: debounce((e) => {
                    //功能 子组件使用 父组件的值  做计算
                    //计算当前输入的值  与  childUserMain组件的值  相乘  赋值给 fieldLong组件
                    record.fieldLong = sum([e, formModel.childUseMain]);

                    // record.fieldLong = (parseInt(e) ?? 0) * (parseInt(formModel.childUseMain) ?? 0);

                    //父组件 使用 子组件做计算
                    //当前子组件的 fieldDouble所有值  合计 赋值给  mainUseChild
                    formModel.mainUseChild = sum(formModel.subform.map((item) => item.fieldDouble));
                  }, 1000),
                };
              },
              // width: 120,
            },
            {
              title: '长整数',
              dataIndex: 'fieldLong',
              componentType: 'Render', //如果使用自定义渲染  必须设置componentType === 'Render'
              rules: [
                {
                  required: true,
                  message: '子表单组件验证案例',
                },
              ],
              componentProps: {
                xxxx: ['xxxx', 'xxxx'],
              },
              render: ({ model, field, rules, componentProps, disabled }) => {
                const handleChange = (e: ChangeEvent) => {
                  model[field] = e.target.value;
                  model['fieldString'] = e.target.value + '浮点控制字符串';
                  console.log('参数有', model, field, rules, componentProps);
                };

                // tsx写法

                // return (
                //   <Input
                //     placeholder="请输入"
                //     v-model:value={model[field]}
                //     onChange={handleChange}
                //   ></Input>
                // );

                //渲染函数写法
                return h(Input, {
                  placeholder: '请输入11111111',
                  disabled,
                  value: model[field],
                  onChange: handleChange,
                });
              },
              // width: 180,
            },
            {
              title: '备注',
              dataIndex: 'remark',
              componentType: 'IconPicker',
            },
          ],
          // width: 120,
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
        },
      ],
    },
  },
];
export const getFormProps: FormProps = {
  // labelCol: { span: 2 },
  // wrapperCol: { span: 6 },
  labelWidth: 80,
  schemas: getFormConfig,
  showActionButtonGroup: false,
  actionColOptions: {
    span: 24,
  },
  showResetButton: true,
  showSubmitButton: true,
};
