<template>
  <a-modal
    width="90%"
    v-model:visible="openModalVisible"
    :title="'关联门店'"
    :footer="null"
    :maskClosable="false"
    destroyOnClose
    centered
    @cancel="onCancel()"
  >
    <div class="modal_box">
      <div class="filterForm_box">
        <a-select
          v-model:value="searchForm.headAccountId"
          style="width: 240px"
          placeholder="连锁总部"
          :options="headOptions"
          :field-names="{ label: 'name', value: 'accountId' }"
          :filter-option="headFilterOption"
          show-search
          allowClear
          @change="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.id"
          placeholder="门店ID"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.shopName"
          placeholder="门店名称"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.partName"
          placeholder="连锁分部名称"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.headName"
          placeholder="连锁总部名称"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.ownerNameOrNumber"
          placeholder="责任人/工号"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-select
          v-model:value="searchForm.isDirectJoin"
          style="width: 240px"
          placeholder="直营/加盟"
          :options="directJoinOptions"
          allowClear
          @change="getList()"
        />
        <a-select
          v-model:value="searchForm.isDtp"
          style="width: 240px"
          placeholder="是否DTP门店"
          :options="options"
          allowClear
          @change="getList()"
        />
        <a-select
          v-model:value="searchForm.isWhole"
          style="width: 240px"
          placeholder="是否统筹门店"
          :options="options"
          allowClear
          @change="getList()"
        />
        <a-select
          v-model:value="searchForm.isOnline"
          style="width: 240px"
          placeholder="是否电商门店"
          :options="options"
          allowClear
          @change="getList()"
        />
        <a-button type="primary" @click="getList()">搜索</a-button>
        <a-button @click="reSet()">重置</a-button>
        <a-button type="primary" :disabled="!selectedKeys.length" @click="cancelConnect()"
          >取消关联</a-button
        >
      </div>
      <div class="table_box">
        <c-table
          :tableColumns="tableColumns"
          :tableData="tableData.data"
          :loading="loading"
          :currentPage="pagination.currentPage"
          :totalItems="pagination.totalItems"
          :pageSize="pagination.pageSize"
          @update:current-page="(value) => (pagination.currentPage = value)"
          @pagination-change="handlePaginationChange"
          :isSelection="true"
          rowKey="id"
          :checkedKeys="selectedKeys"
          @onSelectChange="onSelectChange"
        >
          <template #isDirectJoin="{ record }">
            <span>{{ record.isDirectJoin ? '加盟' : '直营' }}</span>
          </template>
          <template #isOr="{ record, column }">
            <span>{{ record[column.key] ? '是' : '否' }}</span>
          </template>
        </c-table>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import cTable from '/@/views/components/Table/index.vue';
  import { getHeadList } from '/@/api/customerManage/account';
  import { tagConnectCancel, getAccountList } from '/@/api/customerManage/tags';
  import { useMessage } from '/@/hooks/web/useMessage';
  const { notification } = useMessage();
  const emits = defineEmits(['onHide']);
  const openModalVisible = ref(false);
  const onCancel = () => {
    emits('onHide');
  };
  const onShow = (id: any) => {
    searchForm.reqId = id;
    getSearchHeadList();
    reSet();
    openModalVisible.value = true;
  };
  defineExpose({
    onShow,
  });

  const searchForm = reactive({
    id: '',
    reqId: '',
    shopName: '',
    partName: '',
    headName: '',
    ownerNameOrNumber: '',
    headAccountId: null,
    isDirectJoin: null,
    isDtp: null,
    isOnline: null,
    isWhole: null,
  });
  const options = ref([
    {
      value: 1,
      label: '是',
    },
    {
      value: 0,
      label: '否',
    },
  ]);
  const directJoinOptions = ref([
    {
      value: 1,
      label: '加盟',
    },
    {
      value: 0,
      label: '直营',
    },
  ]);
  const reSet = () => {
    searchForm.id = '';
    searchForm.shopName = '';
    searchForm.partName = '';
    searchForm.headName = '';
    searchForm.ownerNameOrNumber = '';
    searchForm.headAccountId = null;
    searchForm.isDirectJoin = null;
    searchForm.isDtp = null;
    searchForm.isOnline = null;
    searchForm.isWhole = null;
    getList();
  };
  const tableColumns = [
    {
      title: '门店ID',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
      fixed: 'left',
      width: 180,
    },
    {
      title: '门店名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      fixed: 'left',
      width: 250,
    },
    {
      title: '连锁分部名称',
      dataIndex: 'parentAccountIdName',
      key: 'parentAccountIdName',
      align: 'center',
      width: 250,
    },
    {
      title: '连锁总部名称',
      dataIndex: 'newOtcAccountName',
      key: 'newOtcAccountName',
      align: 'center',
      width: 250,
    },
    {
      title: '省',
      dataIndex: 'province',
      key: 'province',
      align: 'center',
      width: 80,
    },
    {
      title: '市',
      dataIndex: 'city',
      key: 'city',
      align: 'center',
      width: 80,
    },
    {
      title: '区/县',
      dataIndex: 'district',
      key: 'district',
      align: 'center',
      width: 100,
    },
    {
      title: '所属辖区',
      dataIndex: 'belongRegion',
      key: 'belongRegion',
      align: 'center',
      width: 100,
    },
    {
      title: '责任人',
      dataIndex: 'ownerName',
      key: 'ownerName',
      align: 'center',
      width: 100,
    },
    {
      title: '工号',
      dataIndex: 'ownerNumber',
      key: 'ownerNumber',
      align: 'center',
      width: 150,
    },
    {
      title: '直营/加盟',
      dataIndex: 'isDirectJoin',
      key: 'isDirectJoin',
      align: 'center',
      width: 80,
      isSlot: true,
    },
    {
      title: '门店类型',
      dataIndex: 'customerTypeCodeName',
      key: 'customerTypeCodeName',
      align: 'center',
      width: 80,
    },
    {
      title: '年销售规模(万)',
      dataIndex: 'saleScale',
      key: 'saleScale',
      align: 'center',
      width: 120,
    },
    {
      title: '门店标签',
      dataIndex: 'customerTagName',
      key: 'customerTagName',
      align: 'center',
      width: 90,
    },
    {
      title: '是否DTP药房',
      dataIndex: 'isDtp',
      key: 'isDtp',
      align: 'center',
      width: 100,
      isSlot: true,
      slotName: 'isOr',
    },
    {
      title: '是否统筹门店',
      dataIndex: 'isWhole',
      key: 'isWhole',
      align: 'center',
      width: 100,
      isSlot: true,
      slotName: 'isOr',
    },
    {
      title: '是否电商门店',
      dataIndex: 'isOnline',
      key: 'isOnline',
      align: 'center',
      width: 100,
      isSlot: true,
      slotName: 'isOr',
    },
  ];
  const tableData = reactive({
    data: [],
  });
  const loading = ref(false);
  const pagination = reactive({
    currentPage: 1,
    totalItems: 0,
    pageSize: 10,
  });
  // 表格多选
  const selectedKeys = ref<any[]>([]);
  const onSelectChange = (keys: any[]) => {
    selectedKeys.value = keys;
  };
  // 获取表格数据
  const getList = async (flag?: number) => {
    selectedKeys.value = [];
    if (!flag) {
      pagination.currentPage = 1;
      pagination.pageSize = 10;
    }
    loading.value = true;
    tableData.data = [];
    try {
      let temp = {
        ...searchForm,
        terminalType: 2,
        labelType: 1,
        customerTagId: searchForm.reqId,
        limit: pagination.currentPage,
        size: pagination.pageSize,
      };
      let res = await getAccountList(temp);
      tableData.data = res?.list ?? [];
      pagination.totalItems = res.total ?? 0;
      loading.value = false;
    } catch (error) {
      console.log(error);
      loading.value = false;
    }
  };
  // 处理分页
  const handlePaginationChange = (page: any) => {
    pagination.currentPage = page.current;
    pagination.pageSize = page.pageSize;
    getList(1);
  };

  // 连锁总部列表
  const headOptions = ref([]);
  const getSearchHeadList = async () => {
    try {
      let res = await getHeadList();
      headOptions.value = res ?? [];
    } catch (error) {
      console.log(error);
    }
  };
  const headFilterOption = (input: string, option: any) => {
    return option.name.indexOf(input) >= 0;
  };

  // 取消关联
  const cancelConnectLoading = ref(false);
  const cancelConnect = async () => {
    cancelConnectLoading.value = true;
    try {
      let temp = {
        accountId: searchForm.reqId,
        customerTagIdList: selectedKeys.value,
      };
      await tagConnectCancel(temp);
      notification.success({
        message: '提示',
        description: '操作成功',
      });
      getList();
      cancelConnectLoading.value = false;
      selectedKeys.value = [];
    } catch (error) {
      console.log(error);
      cancelConnectLoading.value = false;
    }
  };
</script>

<style scoped lang="less">
  .modal_box {
    padding: 16px;
    .filterForm_box {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }
    .table_box {
      margin-top: 24px;
    }
  }
</style>
