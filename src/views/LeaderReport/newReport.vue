<template>
  <div>
    <div class="clerkInfoCollect-panel">
      <div class="clerkInfoCollect-panel-title">{{
        frequency === '1' ? '工作日报' : frequency === '3' ? '工作周报' : '工作月报'
      }}</div>
      <div class="button-groups">
        <div class="button-item">
          <div class="report-header">
            <div class="report-date-label">
              <span v-if="frequency === '1'" class="date-title">报告日期</span>
              <span v-if="frequency === '3'" class="date-title">周报周期</span>
              <span v-if="frequency === '2'" class="date-title">报告月期</span>
            </div>
            <div class="report-date-value">
              <span class="date-text">{{ timeWeek }}</span>
              <div class="date-arrow">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path
                    d="M6 4L10 8L6 12"
                    stroke="#666"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
          </div>

          <div class="data-table-container">
            <div class="table-header">
              <div class="header-cell work-cell">重点工作</div>
              <div class="header-cell target-cell">周期目标</div>
              <div class="header-cell actual-cell">实际达成</div>
              <div class="header-cell rate-cell">达成率</div>
            </div>

            <div class="table-body">
              <div class="table-row" v-for="(e, i) in tableList" :key="i">
                <div class="data-cell work-cell">{{ e.importantWork }}</div>
                <div class="data-cell target-cell">{{ e.cycleTarget }}</div>
                <div class="data-cell actual-cell">{{ e.actualAchievement }}</div>
                <div class="data-cell rate-cell">{{ e.achievementRate }}%</div>
              </div>
            </div>
          </div>

          <div class="from-title">问题及反馈</div>

          <a-textarea
            v-model:value="questionFeedback"
            show-count
            placeholder="请填写总结内容"
            :maxlength="500"
            :auto-size="{ minRows: 4, maxRows: 8 }"
            :disabled="!!questionFeedback"
          />
        </div>
      </div>
      <div v-if="!questionFeedback" class="btnNext" @click="saveClick">提交</div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import {
    getOtcWeekReportTaskDetail,
    saveOtcWeekReport,
    getOtcWeekReportDetail,
  } from '/@/api/LeaderReport/newReport';
  import { useRoute } from 'vue-router';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();

  const route = useRoute();
  const questionFeedback = ref('');
  const frequency = ref('');
  const timeWeek = ref('');
  /**
   * 获取当前周的周一到周日日期
   * @returns {Array} 包含周一到周日日期的数组，格式为 YYYY-MM-DD
   */
  // const getCurrentWeekDays = (type) => {
  //   const now = new Date();
  //   const nowDay = now.getDay(); // 0是周日，1-6是周一到周六

  //   // 计算当前周的周一日期
  //   const monday = new Date(now);
  //   monday.setDate(now.getDate() - (nowDay === 0 ? 6 : nowDay - 1));

  //   // 生成周一到周日的日期数组
  //   const weekDays = [];
  //   for (let i = 0; i < 7; i++) {
  //     const day = new Date(monday);
  //     day.setDate(monday.getDate() + i);

  //     // 格式化日期为 YYYY-MM-DD
  //     const year = day.getFullYear();
  //     const month = String(day.getMonth() + 1).padStart(2, '0');
  //     const date = String(day.getDate()).padStart(2, '0');

  //     weekDays.push(`${year}${type}${month}${type}${date}`);
  //   }

  //   return weekDays;
  // };

  /**
   * 获取当前周的起止日期
   * @returns {Object} 包含开始日期和结束日期的对象
   */
  // const getLastWeek = () => {
  //   if (frequency.value === '1') {
  //     return dayjs().format('YYYY.MM.DD');
  //   } else if (frequency.value === '2') {
  //     return dayjs().format('YYYY.MM');
  //   } else {
  //     const weekDays = getCurrentWeekDays('.');
  //     return weekDays[0] + '-' + weekDays[6];
  //   }
  // };

  const tableList = ref();
  const params = ref({});

  onMounted(() => {
    frequency.value = route.query.frequency;
    const createUserId = route.query.createUserId;
    params.value = {
      departmentId: obj.departmentId,
      // postCode: obj.postId,
      // systemUserId: obj.code,
      createUserId,
      frequency: option.frequency,
    };
    if (option.frequency === '1') {
      params.value.reportDate = dayjs().format('YYYY-MM-DD');
    } else if (option.frequency === '2') {
      params.value.reportDate = dayjs().format('YYYY-MM');
    } else {
      params.value.reportDateStart = start;
      params.value.reportDateEnd = end;
    }
    getOtcWeekReportDetail({ ...params.value })
      .then((res) => {
        if (res.code === 0 && res.data) {
          tableList.value = res.data.detailList;
          questionFeedback.value = res.data.questionFeedback;
          if (option.frequency === '1') {
            timeWeek.value = res.data.reportDate.replaceAll('-', '.');
          } else if (option.frequency === '2') {
            timeWeek.value = res.data.reportDate.replaceAll('-', '.');
          } else {
            let endDay = res.data.reportDateEnd.replaceAll('-', '.');
            let startDay = res.data.reportDateStart.replaceAll('-', '.');
            timeWeek.value = startDay + '-' + endDay;
          }
        }
      })
      .catch((error) => {
        console.error('Error:', error);
        throw error; // 将错误抛出，供外部捕获
      });
    // getOtcWeekReportTaskDetail({ ...params.value })
    //   .then((res) => {
    //     if (res.data) {
    //       tableList.value = res.data || [];
    //     }
    //   })
    //   .catch((error) => {
    //     console.error('Error:', error);
    //     throw error; // 将错误抛出，供外部捕获
    //   });
  });
  const saveClick = () => {
    return saveOtcWeekReport({
      ...params.value,
      // otcWeekReportTaskDetailList,
      detailList: tableList.value,
      questionFeedback: questionFeedback.value,
    })
      .then(() => {
        createMessage.success('保存成功');
      })
      .catch((error) => {
        throw error; // 将错误抛出，供外部捕获
      });
  };
</script>

<style scoped lang="less">
  .clerkInfoCollect-panel {
    .clerkInfoCollect-panel-title {
      color: rgba(0, 0, 0, 0.9);
      text-align: center;
      /* 点文本-加粗/20pt bold */
      font-family: 'PingFang SC';
      font-size: 20px;
      font-style: normal;
      font-weight: 600;
      line-height: 28px; /* 140% */
      margin-bottom: 20px;
    }
    padding: 20px 16px;
    overflow-y: auto;
    min-height: 100vh;
    background: url('./login_bg.png') no-repeat center center;
    background-size: cover;
    background-attachment: fixed;

    .button-groups {
      margin-bottom: 20px;

      .button-item {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }
    }
  }

  // 报告头部样式
  .report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 4px;

    .report-date-label {
      .date-title {
        font-size: 14px;
        color: #333;
        font-weight: 400;
      }
    }

    .report-date-value {
      display: flex;
      align-items: center;
      gap: 8px;

      .date-text {
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }

      .date-arrow {
        display: flex;
        align-items: center;
        cursor: pointer;
        opacity: 0.6;
        transition: opacity 0.2s;

        &:hover {
          opacity: 1;
        }
      }
    }
  }

  .from-title {
    margin: 20px 0 12px 0;
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }

  // 数据表格容器
  .data-table-container {
    border-radius: 12px;
    background: #fff;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
  }

  // 表格头部
  .table-header {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;

    .header-cell {
      padding: 14px 8px;
      font-size: 13px;
      font-weight: 500;
      color: #666;
      text-align: center;
      border-right: 1px solid #e9ecef;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      min-width: 0;
      display: flex;
      align-items: center;
      justify-content: center;

      &:last-child {
        border-right: none;
      }

      &.work-cell {
        flex: 2;
        text-align: left;
        padding-left: 16px;
        justify-content: flex-start;
        white-space: normal;
        word-wrap: break-word;
      }

      &.target-cell,
      &.actual-cell,
      &.rate-cell {
        flex: 1;
      }
    }
  }

  // 表格主体
  .table-body {
    .table-row {
      display: flex;
      border-bottom: 1px solid #f0f0f0;
      transition: background-color 0.2s ease;
      min-height: 48px;

      &:hover {
        background-color: #f8f9fa;
      }

      &:last-child {
        border-bottom: none;
      }

      .data-cell {
        padding: 16px 12px;
        font-size: 13px;
        color: #333;
        text-align: center;
        border-right: 1px solid #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &:last-child {
          border-right: none;
        }

        &.work-cell {
          flex: 2;
          text-align: left;
          padding-left: 16px;
          justify-content: flex-start;
          font-weight: 500;
          white-space: normal;
          word-wrap: break-word;
          word-break: break-all;
          line-height: 1.4;
          align-items: flex-start;
          padding-top: 16px;
        }

        &.target-cell,
        &.actual-cell {
          flex: 1;
          font-weight: 600;
          color: #333;
        }

        &.rate-cell {
          flex: 1;
          font-weight: 600;
          color: #52c41a;
        }
      }
    }
  }

  .btnNext {
    height: 48px;
    line-height: 48px;
    text-align: center;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    border-radius: 24px;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    margin-top: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
    border: none;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
    }

    &:active {
      transform: translateY(0);
    }
  }

  // 文本域样式优化
  :deep(.ant-input) {
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    background: #fff;

    &:focus {
      border-color: #ff6b35;
      box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.1);
    }
  }

  :deep(.ant-input::placeholder) {
    color: #999;
  }
</style>
