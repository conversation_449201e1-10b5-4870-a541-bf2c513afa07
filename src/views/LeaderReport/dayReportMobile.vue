<template>
  <div id="dayReport">
    <div class="targetMange_Box">
      <p class="time"
        ><img style="margin-right: 5px" src="../../assets/svg/time.svg" />{{ currentTime }}</p
      >
      <div class="box-item">
        <img class="icon" src="../../assets/svg/all.svg" />
        <div class="chart-slide">
          <div class="chart-container">
            <Chart :data="chartData" width="100%" height="100%" />
          </div>
        </div>
        <div class="table-slide">
          <div class="table-container">
            <a-table
              :columns="totalColumns"
              :data-source="totalData"
              :pagination="false"
              rowKey="id"
              :border="true"
              :scroll="{ x: 800, y: 300 }"
              size="small"
              tableLayout="auto"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'rate'">
                  <Icon
                    v-if="Number(record.yesterdayAverage) < Number(record.todayAverage)"
                    :icon="'toward-up|svg'"
                  />
                  <Icon
                    v-if="Number(record.yesterdayAverage) > Number(record.todayAverage)"
                    :icon="'toward-down|svg'"
                  />
                  <Icon
                    v-if="Number(record.yesterdayAverage) === Number(record.todayAverage)"
                    :icon="'toward-ping|svg'"
                  />
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </div>
      <!-- 代表排名表 -->
      <div class="box-item">
        <img class="icon" src="../../assets/svg/daibiao.svg" />
        <div class="table-container">
          <a-table
            :columns="rankColumns"
            :data-source="rankData"
            :pagination="false"
            rowKey="id"
            :border="true"
            :scroll="{ x: 1500, y: 300 }"
            size="small"
            tableLayout="auto"
          />
        </div>
      </div>
      <div class="box-item">
        <img class="icon" src="../../assets/svg/wenti.svg" />
        <div class="table-container">
          <a-table
            :columns="questionColumns"
            :data-source="questionData"
            :pagination="false"
            rowKey="id"
            :border="true"
            :scroll="{ x: 1000, y: 300 }"
            size="small"
            tableLayout="auto"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import Chart from '../performanceReport/components/chart.vue';
  import {
    getPerformanceRadarData,
    getPerformanceBottomRanking,
    getPerformanceKpiTrend,
    getPerformanceRanking,
  } from '/@/api/LeaderReport/index';
  import dayjs from 'dayjs';
  import { useRoute } from 'vue-router';
  import { Icon } from '/@/components/Icon';

  const route = useRoute();
  const currentTime = ref('');
  const chartData = ref<any>({
    data: [],
    indicator: [],
  });

  // 计算行合并的函数
  const calculateRowSpan = (data: any[], index: number, field: string) => {
    if (index === 0) {
      // 第一行，计算连续相同值的数量
      let count = 1;
      const currentValue = data[index][field];
      for (let i = index + 1; i < data.length; i++) {
        if (data[i][field] === currentValue) {
          count++;
        } else {
          break;
        }
      }
      return count;
    } else {
      // 非第一行，检查是否与前一行相同
      if (data[index][field] === data[index - 1][field]) {
        return 0; // 合并到前一行
      } else {
        // 计算从当前行开始连续相同值的数量
        let count = 1;
        const currentValue = data[index][field];
        for (let i = index + 1; i < data.length; i++) {
          if (data[i][field] === currentValue) {
            count++;
          } else {
            break;
          }
        }
        return count;
      }
    }
  };

  const totalColumns = ref([
    {
      title: '目标类型',
      dataIndex: 'taskName',
      key: 'taskName',
      align: 'center',
      width: 100,
      minWidth: 100,
      customRender: ({ text, index }: any) => {
        const rowSpan = calculateRowSpan(totalData.value, index, 'taskName');
        return {
          children: text,
          props: {
            rowSpan: rowSpan,
          },
        };
      },
    },
    {
      title: 'KPI名称',
      dataIndex: 'kpiName',
      key: 'kpiName',
      align: 'center',
      width: 100,
      minWidth: 100,
    },
    {
      title: '目标值',
      dataIndex: 'real',
      key: 'real',
      align: 'center',
      width: 80,
      minWidth: 80,
    },
    {
      title: '昨日',
      dataIndex: 'yesterdayAverage',
      key: 'yesterdayAverage',
      align: 'center',
      width: 80,
      minWidth: 80,
    },
    {
      title: '今日',
      dataIndex: 'todayAverage',
      key: 'todayAverage',
      align: 'center',
      width: 80,
      minWidth: 80,
    },
    {
      title: '趋势',
      dataIndex: 'rate',
      key: 'rate',
      align: 'center',
      width: 80,
      minWidth: 80,
    },
  ]);
  const totalData = ref<any[]>([]);
  const rankColumns = ref<any[]>([]);

  // 生成动态列配置
  const generateRankColumns = (data: any[]) => {
    // 固定列
    const fixedColumns = [
      {
        title: '员工姓名',
        dataIndex: 'saleManName',
        key: 'saleManName',
        align: 'center',
        width: 100,
        minWidth: 100,
      },
      {
        title: 'EDP工号',
        dataIndex: 'edpCode',
        key: 'edpCode',
        align: 'center',
        width: 100,
        minWidth: 100,
      },
      {
        title: '所属辖区',
        dataIndex: 'departNames',
        key: 'departNames',
        align: 'center',
        width: 180,
        minWidth: 180,
      },
      {
        title: '绩效总分（日）',
        dataIndex: 'totalScore',
        key: 'totalScore',
        align: 'center',
        width: 120,
        minWidth: 120,
      },
      {
        title: '全国排名',
        dataIndex: 'nationalRank',
        key: 'nationalRank',
        align: 'center',
        width: 100,
        minWidth: 100,
      },
      {
        title: '省区排名',
        dataIndex: 'provincialRank',
        key: 'provincialRank',
        align: 'center',
        width: 100,
        minWidth: 100,
      },
    ];

    // 动态列 - 根据taskKpiList生成
    const dynamicColumns: any[] = [];

    if (data && data.length > 0 && data[0].taskKpiList) {
      // 遍历taskKpiList，每个task创建一个合并列
      data[0].taskKpiList.forEach((task: any) => {
        const { taskName, kpiList } = task;

        if (kpiList && kpiList.length > 0) {
          // 为每个task的kpiList创建子列
          const children = kpiList.map((kpi: any) => ({
            title: kpi.kpiName, // 小表头
            dataIndex: `kpi_${taskName}_${kpi.kpiName}`,
            key: `kpi_${taskName}_${kpi.kpiName}`,
            align: 'center',
            width: 100,
            minWidth: 100,
            customRender: ({ record }: any) => {
              // 在record的taskKpiList中找到对应的task，然后在其kpiList中找到对应的kpi
              const taskData = record.taskKpiList?.find((item: any) => item.taskName === taskName);
              if (taskData && taskData.kpiList) {
                const kpiData = taskData.kpiList.find((item: any) => item.kpiName === kpi.kpiName);
                return kpiData ? kpiData.real : '-'; // 绑定real字段
              }
              return '-';
            },
          }));

          // 创建父列（合并列）- taskName作为大表头
          dynamicColumns.push({
            title: taskName, // 大表头
            key: taskName,
            align: 'center',
            children: children,
          });
        }
      });
    }

    return [...fixedColumns, ...dynamicColumns];
  };
  const rankData = ref<any[]>([]);

  const questionColumns = ref([
    {
      title: '员工姓名',
      dataIndex: 'saleManName',
      key: 'saleManName',
      align: 'center',
      width: 100,
      minWidth: 100,
    },
    {
      title: 'EDP工号',
      dataIndex: 'edpCode',
      key: 'edpCode',
      align: 'center',
      width: 100,
      minWidth: 100,
    },
    {
      title: '所属辖区',
      dataIndex: 'departNames',
      key: 'departNames',
      align: 'center',
      width: 180,
      minWidth: 180,
    },
    {
      title: '绩效总分（日）',
      dataIndex: 'totalScore',
      key: 'totalScore',
      align: 'center',
      width: 120,
      minWidth: 120,
    },
    {
      title: '全国排名',
      dataIndex: 'nationalRank',
      key: 'nationalRank',
      align: 'center',
      width: 100,
      minWidth: 100,
    },
    {
      title: '省区排名',
      dataIndex: 'provincialRank',
      key: 'provincialRank',
      align: 'center',
      width: 100,
      minWidth: 100,
    },
    {
      title: '未达标项',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      width: 100,
      minWidth: 100,
    },
    {
      title: '原因分析',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      width: 150,
      minWidth: 150,
    },
  ]);

  const questionData = ref<any[]>([]);

  // Mock数据 - 符合新的数据结构
  const mockRankingData = [
    {
      currentTime: '2025-01-17',
      departNames: '华东大区-上海分公司',
      edpCode: 'EDP001',
      hierarchy: '3',
      kpiJson: '{}',
      nationalRank: 1,
      provincialDeptId: 'DEPT001',
      provincialRank: 1,
      saleManName: '张三',
      totalScore: 95.5,
      taskKpiList: [
        {
          taskName: '拜访任务',
          kpiList: [
            {
              kpiName: '拜访完成率',
              real: '95%',
              score: 95,
              weight: 20,
            },
            {
              kpiName: '拜访质量',
              real: '90%',
              score: 90,
              weight: 15,
            },
          ],
        },
        {
          taskName: '销售任务',
          kpiList: [
            {
              kpiName: '销售额',
              real: '120万',
              score: 88,
              weight: 25,
            },
            {
              kpiName: '新客户开发',
              real: '15个',
              score: 92,
              weight: 20,
            },
          ],
        },
        {
          taskName: '培训任务',
          kpiList: [
            {
              kpiName: '培训完成率',
              real: '100%',
              score: 100,
              weight: 10,
            },
            {
              kpiName: '考试成绩',
              real: '85分',
              score: 85,
              weight: 10,
            },
          ],
        },
      ],
    },
    {
      currentTime: '2025-01-17',
      departNames: '华南大区-广州分公司',
      edpCode: 'EDP002',
      hierarchy: '3',
      kpiJson: '{}',
      nationalRank: 2,
      provincialDeptId: 'DEPT002',
      provincialRank: 1,
      saleManName: '李四',
      totalScore: 92.3,
      taskKpiList: [
        {
          taskName: '拜访任务',
          kpiList: [
            {
              kpiName: '拜访完成率',
              real: '88%',
              score: 88,
              weight: 20,
            },
            {
              kpiName: '拜访质量',
              real: '92%',
              score: 92,
              weight: 15,
            },
          ],
        },
        {
          taskName: '销售任务',
          kpiList: [
            {
              kpiName: '销售额',
              real: '115万',
              score: 85,
              weight: 25,
            },
            {
              kpiName: '新客户开发',
              real: '18个',
              score: 95,
              weight: 20,
            },
          ],
        },
        {
          taskName: '培训任务',
          kpiList: [
            {
              kpiName: '培训完成率',
              real: '95%',
              score: 95,
              weight: 10,
            },
            {
              kpiName: '考试成绩',
              real: '90分',
              score: 90,
              weight: 10,
            },
          ],
        },
      ],
    },
    {
      currentTime: '2025-01-17',
      departNames: '华北大区-北京分公司',
      edpCode: 'EDP003',
      hierarchy: '3',
      kpiJson: '{}',
      nationalRank: 3,
      provincialDeptId: 'DEPT003',
      provincialRank: 2,
      saleManName: '王五',
      totalScore: 89.7,
      taskKpiList: [
        {
          taskName: '拜访任务',
          kpiList: [
            {
              kpiName: '拜访完成率',
              real: '92%',
              score: 92,
              weight: 20,
            },
            {
              kpiName: '拜访质量',
              real: '85%',
              score: 85,
              weight: 15,
            },
          ],
        },
        {
          taskName: '销售任务',
          kpiList: [
            {
              kpiName: '销售额',
              real: '110万',
              score: 82,
              weight: 25,
            },
            {
              kpiName: '新客户开发',
              real: '12个',
              score: 88,
              weight: 20,
            },
          ],
        },
        {
          taskName: '培训任务',
          kpiList: [
            {
              kpiName: '培训完成率',
              real: '98%',
              score: 98,
              weight: 10,
            },
            {
              kpiName: '考试成绩',
              real: '88分',
              score: 88,
              weight: 10,
            },
          ],
        },
      ],
    },
  ];

  const intData = async (deptId: any, frequency: any) => {
    // const tmp = {
    //   deptId,
    //   currentTime: currentTime.value,
    //   frequency,
    // };
    // const res = await Promise.all([
    //   await getPerformanceRadarData(tmp), // 雷达
    //   await getPerformanceRanking(tmp), // 排名
    //   await getPerformanceBottomRanking(tmp), // 排名
    //   await getPerformanceKpiTrend(tmp), // 趋势
    // ]);
    // chartData.value.indicator = res[0][1].taskKpiList.map((item) => {
    //   return {
    //     text: item.taskName,
    //     max: item.weight,
    //   };
    // });
    // const data = [
    //   {
    //     value: res[0][0].taskKpiList.map((item) => {
    //       return item.score;
    //     }),
    //     name: '昨日',
    //   },
    //   {
    //     value: res[0][1].taskKpiList.map((item) => {
    //       return item.score;
    //     }),
    //     name: '今日',
    //   },
    // ];
    // chartData.value.data = data as any;
    const a = [
      {
        taskKpiList: [
          {
            score: 20,
            taskName: '2',
            weight: 100,
          },
        ],
      },
      {
        taskKpiList: [
          {
            score: 70,
            taskName: '2',
            weight: 120,
          },
        ],
      },
    ];
    chartData.value.indicator = a[1].taskKpiList.map((item) => {
      return {
        text: item.taskName,
        max: item.weight,
      };
    });
    const data = [
      {
        value: a[0].taskKpiList.map((item) => {
          return item.score;
        }),
        name: '昨日',
      },
      {
        value: a[1].taskKpiList.map((item) => {
          return item.score;
        }),
        name: '今日',
      },
    ];
    chartData.value.data = data as any;
    console.log('chartData', chartData.value);

    // 处理排名数据并生成动态列
    console.log('使用Mock数据测试代表排名表功能');
    console.log('Mock数据:', mockRankingData);

    // 为每条数据添加唯一ID
    const processedRankData = mockRankingData.map((item: any, index: number) => ({
      ...item,
      id: item.edpCode || `rank_${index}`, // 使用EDP工号作为ID，如果没有则使用索引
    }));

    rankData.value = processedRankData;

    // 生成动态列配置
    rankColumns.value = generateRankColumns(processedRankData);
    console.log('生成的列配置:', rankColumns.value);
    console.log('处理后的数据:', processedRankData);

    totalData.value = [
      {
        id: 'total_1',
        kpiName: '拜访完成率',
        real: '95%',
        taskName: '拜访任务',
        todayAverage: '90',
        yesterdayAverage: '85',
        rate: '+5%',
      },
      {
        id: 'total_2',
        kpiName: '拜访质量',
        real: '88%',
        taskName: '拜访任务',
        todayAverage: '85',
        yesterdayAverage: '82',
        rate: '+3%',
      },
      {
        id: 'total_3',
        kpiName: '销售额',
        real: '120万',
        taskName: '销售任务',
        todayAverage: '115',
        yesterdayAverage: '120',
        rate: '+4.5%',
      },
      {
        id: 'total_4',
        kpiName: '新客户开发',
        real: '15个',
        taskName: '销售任务',
        todayAverage: '12',
        yesterdayAverage: '10',
        rate: '+20%',
      },
      {
        id: 'total_5',
        kpiName: '客户维护',
        real: '98%',
        taskName: '销售任务',
        todayAverage: '95',
        yesterdayAverage: '93',
        rate: '+2%',
      },
      {
        id: 'total_6',
        kpiName: '培训完成率',
        real: '100%',
        taskName: '培训任务',
        todayAverage: '98',
        yesterdayAverage: '98',
        rate: '+3%',
      },
    ];
    console.log('totalData设置完成:', totalData.value);
    questionData.value = [];
  };
  onMounted(() => {
    currentTime.value = dayjs().format('YYYY-MM-DD');
    const { deptId } = route.query;
    // frequency 1日2月3周
    const deptIdC = deptId || 1;
    intData(deptIdC, '1');
  });
</script>

<style scoped lang="less">
  ::v-deep {
    .ant-table.ant-table-small .ant-table-tbody .ant-table-wrapper:only-child .ant-table {
      margin: 0px !important;
    }
  }
  #dayReport {
    width: 100%;
    min-height: 100vh;
    .targetMange_Box {
      width: 100%;
      height: 100%;
      .title {
        font-size: 20px;
        text-align: center;
        margin-bottom: 20px;
        margin-right: 10px;
      }
      .time {
        width: 146px;
        height: 28px;
        color: #ff9201;

        /* 点文本-加粗/14pt bold */
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 24px;
        background: rgba(255, 255, 255, 0.25);
      }
      .subtitle {
        font-size: 16px;
        display: flex;
        background: #ffedd0;
        height: 30px;
        line-height: 30px;
        font-weight: bold;
        &::before {
          content: '';
          width: 4px;
          height: 100%;
          background: #f9c048;
          display: inline-block;
          margin-right: 10px;
        }
      }
      .chart-slide,
      .table-slide {
        height: 100%;
        width: 100%;
        padding: 0 10px;
      }

      .chart-container {
        height: 280px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .table-container {
        height: 100%;
        width: 100%;
        overflow: hidden;

        :deep(.ant-table-wrapper) {
          height: 100%;
        }

        :deep(.ant-table) {
          height: 100%;
          table-layout: auto; // 允许表格自动调整列宽
        }

        :deep(.ant-table-container) {
          height: 100%;
        }

        :deep(.ant-table-header) {
          overflow: hidden; // 表头不滚动
        }

        :deep(.ant-table-body) {
          height: calc(100% - 55px); // 减去表头高度
          overflow: auto;
        }

        :deep(.ant-table-thead > tr > th) {
          position: sticky;
          top: 0;
          z-index: 1;
          background: #fafafa;
          white-space: nowrap;
          padding: 8px 4px;
          font-size: 12px;
          min-width: 80px; // 设置最小宽度防止挤压
          text-overflow: ellipsis;
          border-right: 1px solid #f0f0f0;
        }

        :deep(.ant-table-tbody > tr > td) {
          white-space: nowrap;
          padding: 8px 4px;
          font-size: 12px;
          min-width: 80px; // 设置最小宽度防止挤压
          text-overflow: ellipsis;
          border-right: 1px solid #f0f0f0;
        }

        // 特殊处理较长的列
        :deep(.ant-table-thead > tr > th:nth-child(3)),
        :deep(.ant-table-tbody > tr > td:nth-child(3)) {
          min-width: 120px; // 所属辖区列需要更宽
        }

        :deep(.ant-table-thead > tr > th:nth-child(8)),
        :deep(.ant-table-tbody > tr > td:nth-child(8)) {
          min-width: 100px; // 原因分析列需要更宽
        }
      }
      .box-item {
        box-sizing: border-box;
        width: 100%;
        margin-top: 20px;
        overflow: hidden;
        border-radius: 24px;
        background: #fff;

        /* 下层投影 */
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
        padding: 40px 10px 10px 10px;
        position: relative;
        .icon {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 100;
        }
      }
    }
  }

  /* 媒体查询 - 宽度小于等于540px */
  @media (max-width: 540px) {
    #dayReport {
      padding: 8px;
      background: url('../../assets/svg/ri-bg.svg') no-repeat;
      background-size: contain;

      .targetMange_Box {
        padding: 10px;
        .time {
          margin-top: 15vh;
        }
      }
    }
  }

  /* 媒体查询 - 宽度大于540px */
  @media (min-width: 541px) {
    #dayReport {
      background: url('../../assets/svg/ri_bg.svg') no-repeat;
      background-size: contain;
      padding: 8px;

      .targetMange_Box {
        padding: 10px;

        .time {
          margin: 9% auto 0;
        }
      }
    }
  }
</style>
