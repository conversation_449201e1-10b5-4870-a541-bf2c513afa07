export interface Column {
  title: string;
  key: string;
  isSlot?: boolean;
  dataIndex?: string;
  align?: string;
  fixed?: boolean | string;
  [key: string]: any;
}
export interface Props {
  // 表格项
  tableColumns: Column[];
  // 表格数据
  tableData: object[];
  // 加载效果
  loading?: boolean;
  // 当前页
  currentPage: number;
  // 每页显示条数
  pageSize: number;
  // 是否显示总条目数
  showTotal?: boolean;
  // 总条目数
  totalItems: number;
  // 是否可以改变每页显示的条数
  showSizeChanger?: boolean;
  // 可选的每页显示条数
  pageSizeOptions?: (number | string)[];
  // 是否可以快速跳转到指定页
  showQuickJumper?: boolean;
  // 表格滚动
  scroll?: object;
  // 是否显示合计行
  isSummary?: boolean;
  // 是否支持选择功能
  isSelection?: boolean;
  // 唯一标识
  rowKey: string;
  // 选中项的 key 数组
  checkedKeys?: (number | string)[];
  // 列表项是否可选择
  rowSelection?: object;
  border?: boolean;
}
