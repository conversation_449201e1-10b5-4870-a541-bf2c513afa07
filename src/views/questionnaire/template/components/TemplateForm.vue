<template>
  <div :class="{ 'questionnaire-box': params.from === 'link' }">
    <div class="questionnaire-title" v-if="params.from === 'link'">{{ formName }}</div>
    <div v-if="params.from === 'link'">
      <img :src="leftPng" class="left-png" />
      <img :src="rightPng" class="right-png" />
    </div>
    <div class="questionnaire-content">
      <SimpleForm
        ref="formRef"
        :formProps="formProps"
        :formModel="state.formModel"
        @submit="handleSubmit"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, unref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import SimpleForm from '/@/views/questionnaire/design/components/components/SimpleForm.vue';
  import { useUserStore } from '/@/store/modules/user';
  import { addQnExecute } from '/@/api/questionnaire/template';
  import { guestLogin } from '/@/api/system/login';
  import { getQuestionnaireInfo } from '/@/api/questionnaire/design';
  import { buildOption } from '/@/utils/helper/designHelper';
  import { useTitle } from '@vueuse/core';
  import leftPng from '/@/assets/questionnaire/left.png';
  import rightPng from '/@/assets/questionnaire/right.png';

  const emit = defineEmits(['success', 'register']);
  const { notification } = useMessage();
  const userStore = useUserStore();
  const formRef = ref();
  const menuId = ref('');
  const { currentRoute } = useRouter();
  const { params } = unref(currentRoute);
  const menuName = ref('');
  const menuCode = ref('');
  const formId = ref('');
  const formName = ref('');

  menuId.value = params.id as string;
  const formProps = ref<any>({});
  const state = reactive({
    formModel: {},
    isUpdate: true,
    isView: false,
    isCopy: false,
    rowId: '',
    pkField: 'id',
    formEventConfig: [],
    menuName: '',
  });

  onMounted(async () => {
    const token = await guestLogin();
    userStore.setToken(token);
    const data = await getQuestionnaireInfo(menuId.value as string);
    formId.value = data.id;
    formName.value = data.name;
    const configJson = JSON.parse(data.content);
    menuName.value = configJson.menuConfig.name;
    menuCode.value = configJson.menuConfig.code;
    //设置浏览器标题
    useTitle(menuName.value);
    //构建表单Props
    formRef.value.setProps({
      ...buildOption(configJson.formJson, false),
      layout: 'vertical',
      showResetButton: true,
      showSubmitButton: true,
    });
    formRef.value.setDefaultValue();
    formRef.value.resetFields();
  });

  const { t } = useI18n();

  async function handleSubmit(values) {
    try {
      await addQnExecute({ id: formId.value, formData: values });

      notification.success({
        message: t('提示'),
        description: t('新增成功！'),
      }); //提示消息

      formRef.value.resetFields();
      emit('success');
    } catch (error) {
    } finally {
    }
  }
</script>
<style lang="less">
  .questionnaire-title {
    text-align: center;
    line-height: 60px;
    font-size: 20px;
    color: #5e95ff;
    font-weight: bold;
    background: #fff;
    box-shadow: 0 5px 5px #eff1f3;
  }

  .questionnaire-content {
    padding-bottom: 10px;
  }

  .questionnaire-box {
    background: #f3f6fa;
    min-height: 100%;

    img {
      position: absolute;
    }

    .left-png {
      left: 220px;
      top: 70px;
      width: 70px;
    }

    .right-png {
      right: 220px;
      top: 30px;
      width: 130px;
    }

    .questionnaire-content {
      position: relative;
      margin: 35px 250px;
      background: #fff;
      box-shadow: 0 30px 20px 5px #eff1f3;
    }

    .questionnaire-form {
      min-height: calc(100vh - 190px);
    }
  }

  .questionnaire-form {
    min-height: calc(100vh - 60px);
  }
</style>
