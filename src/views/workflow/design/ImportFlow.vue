<template>
  <span @click.stop="open"
    ><slot></slot>
    <a-modal
      v-model:visible="data.visible"
      :title="t('导入流程')"
      :maskClosable="false"
      @ok="close"
      @cancel="close"
      @click.stop=""
    >
      <div class="upload-box">
        <a-upload
          class="upload-box"
          name="file"
          accept=".json,.txt"
          :headers="data.headers"
          :max-count="1"
          :showUploadList="false"
          :action="data.action"
          @change="handleChange"
        >
          <img :src="BgImg" />
          <div class="a-upload__text"
            >{{ t('将文件拖到此处，或') }}<em>{{ t('点击上传') }}</em></div
          >
        </a-upload>
      </div>
    </a-modal>
  </span>
</template>

<script setup lang="ts">
  import { reactive } from 'vue';
  import BgImg from '/@/assets/workflow/process_import.png';
  import { getAppEnvConfig } from '/@/utils/env';
  import { getToken } from '/@/utils/auth';
  import type { UploadChangeParam } from 'ant-design-vue';
  import { message } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  let emits = defineEmits(['close']);
  const data: {
    visible: boolean;
    action: string;
    headers: { Authorization: string };
  } = reactive({
    visible: false,
    action: '',
    headers: { Authorization: '' },
  });

  async function open() {
    data.visible = true;
    data.action = getAppEnvConfig().VITE_GLOB_API_URL + '/workflow/schema/import';
    data.headers.Authorization = `Bearer ${getToken()}`;
  }
  function close() {
    data.visible = false;
    emits('close');
  }
  function handleChange(info: UploadChangeParam) {
    if (info.file.status !== 'uploading') {
      console.log(info.file, info.fileList);
    }
    if (info.file.status === 'done') {
      if (info.file && info.file.response && info.file.response.code == 0) {
        message.success(t(`{name}导入成功！`, { name: info.file.name }));
        close();
      } else {
        message.error(t(info.file.response.msg || '导入流程失败！'));
      }
    } else if (info.file.status === 'error') {
      message.error(t(`{name}上传失败.`, { name: info.file.name }));
    }
  }
</script>
<style lang="less" scoped>
  .upload-box {
    display: inline-block;
  }

  .upload-demo {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .a-upload-dragger {
    width: 615px;
    height: 370px;
    border: none;
  }

  .a-upload__text {
    position: absolute;
    bottom: 100px;
    right: 100px;
    font-weight: bold;
    color: #1d2027;
  }

  em {
    font-style: normal;
    color: #4f83fd;
  }
</style>
