<template>
  <BasicPanel v-if="showPanel">
    <template #basic>
      <FormItem :label="t('当前进度：')">
        <a-input-number
          v-model:value="formInfo.currentProgress"
          style="width: 100%"
          :min="0"
          :max="100"
        >
          <template #addonAfter>%</template>
        </a-input-number>
      </FormItem>
    </template>
    <template #noticePolicy>
      <FormItem :label="t('通知策略：')">
        <NoticePolicyConfig v-model="formInfo.noticePolicyConfigs" />
      </FormItem>
      <NoticeMsgConfig v-model:noticeMessageConfigs="formInfo.noticeMessageConfigs" />
    </template>
    <a-tab-pane key="2" :tab="t('审批人')">
      <ApproveRules
        v-model:autoAgreeRule="formInfo.autoAgreeRule"
        v-model:noHandler="formInfo.noHandler"
        v-model:isPrevChooseNext="formInfo.isPrevChooseNext"
        v-model:provisionalApprover="formInfo.provisionalApprover"
      />
      <MemberTable
        v-model:memberList="formInfo.approverConfigs"
        :is-common-type="false"
        :is-api-approver="true"
      />
    </a-tab-pane>
    <a-tab-pane key="3" :tab="t('传阅人')">
      <MemberTable
        v-model:memberList="formInfo.circulateConfigs"
        :is-common-type="false"
        :is-api-approver="true"
      />
    </a-tab-pane>
    <a-tab-pane key="4" :tab="t('表单设置')"><SettingList /></a-tab-pane>
    <a-tab-pane key="5" :tab="t('会签')"><Countersign /></a-tab-pane>
    <a-tab-pane key="6" :tab="t('按钮设置')"><ButtonSetting /></a-tab-pane>
    <a-tab-pane key="7" :tab="t('关联意见框')"><OpinionConfig /></a-tab-pane>
    <a-tab-pane key="8" :tab="t('参数操作')"> <ParameterOperation /></a-tab-pane>
    <a-tab-pane key="9" :tab="t('超时处理')"> <TimeOutHandle /> </a-tab-pane>
  </BasicPanel>
</template>

<script setup lang="ts">
  import useStateFormInfo from '/@bpmn/hooks/useStateFormInfo';
  import BasicPanel from '/@bpmn/components/BasicPanel.vue';
  import FormItem from '/@bpmn/layout/FormItem.vue';
  import NoticePolicyConfig from '/@bpmn/components/NoticePolicyConfig.vue';
  import NoticeMsgConfig from '/@bpmn/components/NoticeMsgConfig.vue';
  import SettingList from '/@bpmn/components/formSettings/SettingList.vue';
  import MemberTable from '/@bpmn/components/member/MemberTable.vue';
  import ApproveRules from '/@bpmn/components/ApproveRules.vue';
  import Countersign from './user/Countersign.vue';
  import ButtonSetting from './user/ButtonSetting.vue';
  import OpinionConfig from './user/OpinionConfig.vue';
  import TimeOutHandle from './user/TimeOutHandle.vue';
  import ParameterOperation from '/@bpmn/components/parameters/ParameterOperation.vue';
  import { watchEffect } from 'vue';
  import { CountersignMemberConfig } from '/@/model/workflow/memberSetting';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const { showPanel, formInfo } = useStateFormInfo();

  watchEffect(() => {
    if (formInfo.value?.approverConfigs) {
      changeCountersignConfig();
    }
  });
  function changeCountersignConfig() {
    let checkedIds: Array<string> = [];
    if (
      formInfo.value?.countersignConfig.countersignList &&
      formInfo.value.countersignConfig.countersignList.length > 0
    ) {
      checkedIds = formInfo.value.countersignConfig.countersignList
        .filter((ele: CountersignMemberConfig) => {
          return ele.checked;
        })
        .map((ele: CountersignMemberConfig) => {
          return ele.id;
        });
    }
    if (formInfo.value?.approverConfigs) {
      formInfo.value.countersignConfig.countersignList = formInfo.value?.approverConfigs.map(
        (ele: CountersignMemberConfig) => {
          ele.checked = checkedIds.length > 0 && checkedIds.includes(ele.id) ? true : false;
          return ele;
        },
      );
    }
  }
</script>

<style scoped></style>
