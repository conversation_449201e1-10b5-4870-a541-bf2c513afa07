<template>
  <div>
    <FormItem :label="t('是否启用：')">
      <a-switch v-model:checked="processInfo.timeoutRemidConfig.enabled" />
    </FormItem>

    <div v-show="showConfig">
      <FormItem required :label="t('超时时间：')">
        <a-input-number
          id="inputNumber"
          :placeholder="t('请填写超时时间(小时)')"
          v-model:value="processInfo.timeoutRemidConfig.hour"
          :min="1"
          style="width: 100%"
        />
      </FormItem>

      <FormItem required :label="t('消息间隔：')">
        <a-input-number
          id="inputNumber"
          :placeholder="t('请填写消息间隔(小时)')"
          v-model:value="processInfo.timeoutRemidConfig.interval"
          :min="1"
          style="width: 100%"
        />
      </FormItem>

      <FormItem required :label="t('推送次数：')">
        <a-input-number
          id="inputNumber"
          :placeholder="t('请填写推送次数')"
          v-model:value="processInfo.timeoutRemidConfig.pushHits"
          :min="1"
          style="width: 100%"
        />
      </FormItem>

      <MemberTable
        v-model:memberList="processInfo.timeoutRemidConfig.pushMemberConfigs"
        :isCommonType="false"
        :isUpper="true"
      />
    </div>
  </div>
</template>

<script setup lang="ts" name="ProcessTimeout">
  import { storeToRefs } from 'pinia';
  import { computed } from 'vue';
  import FormItem from '/@bpmn/layout/FormItem.vue';
  import { useBpmnStore } from '/@bpmn/store/bpmn';
  import MemberTable from '/@bpmn/components/member/MemberTable.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const store = useBpmnStore();
  const { processInfo } = storeToRefs(store);
  const showConfig = computed(() => {
    return processInfo.value.timeoutRemidConfig.enabled;
  });
</script>

<style scoped></style>
