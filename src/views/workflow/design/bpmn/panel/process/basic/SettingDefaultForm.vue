<template>
  <DefaultFormSettingModal
    :list="processInfo.defaultFormList"
    :isSingle="false"
    @submit="submit"
    @close="close"
    :key="renderKey"
  >
    <InputModel :placeholder="t('请选择默认表单')" :value="processInfo.defaultFormList.length">
      <div v-if="processInfo.defaultFormList && processInfo.defaultFormList.length > 0">
        <a-tag
          v-for="(item, index) in processInfo.defaultFormList"
          :key="index"
          closable
          @close="closeTag(index)"
          >{{ item.formName }}</a-tag
        >
      </div>
    </InputModel>
  </DefaultFormSettingModal>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';
  import DefaultFormSettingModal from '/@bpmn/components/formSettings/DefaultFormSettingModal.vue';
  import InputModel from '/@bpmn/components/InputModel.vue';
  import { FormSettingItem } from '/@/model/workflow/formSetting';
  import { useBpmnStore } from '/@bpmn/store/bpmn';
  import { nodesAppendDefaultForm } from '/@bpmn/config/property';
  import { formPermissionList } from '/@bpmn/config/formPermission';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { ref } from 'vue';
  const { t } = useI18n();
  const renderKey = ref(0);
  const store = useBpmnStore();
  const { processInfo } = storeToRefs(store);
  async function submit(list: Array<FormSettingItem>) {
    let defaultFormList = list;
    processInfo.value.defaultFormList = defaultFormList;
    let returnArr = await formPermissionList(defaultFormList);
    nodesAppendDefaultForm(returnArr);
    close();
  }
  function close() {
    renderKey.value++;
  }
  function closeTag(index: number) {
    processInfo.value.defaultFormList.splice(index, 1);
  }
</script>

<style lang="less" scoped>
  :deep(.slot-item) {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .open-icon {
    text-align: center;
    height: 30px;
  }
</style>
