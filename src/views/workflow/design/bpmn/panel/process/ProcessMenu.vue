<template>
  <div>
    <FormItem label="是否启用：">
      <a-switch v-model:checked="processInfo.menuConfig.enabled" />
    </FormItem>

    <div v-show="showConfig">
      <FormItem required label="功能编号：">
        <a-input v-model:value="processInfo.menuConfig.code" placeholder="请填写功能编号" />
      </FormItem>
      <FormItem required label="功能名称：">
        <a-input v-model:value="processInfo.menuConfig.name" placeholder="请填写功能名称" />
      </FormItem>

      <FormItem required label="所属系统：">
        <a-select
          v-model:value="processInfo.menuConfig.system"
          style="width: 100%"
          :options="systematics"
          placeholder="请填写所属系统"
        />
      </FormItem>

      <FormItem label="上级功能：">
        <MenuSelect v-model:value="processInfo.menuConfig.parentId" />
      </FormItem>
      <FormItem label="功能图标：">
        <IconPicker v-model:value="processInfo.menuConfig.icon" />
      </FormItem>
      <FormItem label="序号：">
        <a-input-number
          id="inputNumber"
          placeholder="请填写序号"
          v-model:value="processInfo.menuConfig.order"
          :min="1"
          :max="10000"
          style="width: 100%"
        />
      </FormItem>

      <FormItem label="备注：">
        <a-textarea v-model:value="processInfo.menuConfig.remark" :rows="8" />
      </FormItem>
    </div>
  </div>
</template>

<script setup lang="ts" name="ProcessMenu">
  import { storeToRefs } from 'pinia';
  import { computed } from 'vue';
  import { useBpmnStore } from '/@bpmn/store/bpmn';
  import FormItem from '/@bpmn/layout/FormItem.vue';
  import { IconPicker } from '/@/components/Icon';
  import { MenuSelect } from '/@/components/MenuSelect';
  const store = useBpmnStore();
  const { processInfo } = storeToRefs(store);
  const showConfig = computed(() => {
    return processInfo.value.menuConfig.enabled;
  });
  // 所属系统
  const systematics = [
    {
      value: 'main',
      label: '主系统',
    },
  ];
</script>

<style scoped></style>
