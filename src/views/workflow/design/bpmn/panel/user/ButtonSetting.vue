<template>
  <div class="list-box">
    <div class="opr-box">
      <NodeHead :nodeName="t('按钮列表')" />
      <div class="button-box">
        <a-button type="primary" @click="addItem">{{ t('添加按钮') }}</a-button>
      </div>
    </div>
    <div class="list">
      <div class="row head">
        <span class="small">{{ t('选择') }}</span>
        <span class="common">{{ t('按钮名称') }}</span>
        <span class="common">{{ t('按钮编码') }}</span>
        <span class="big">{{ t('使用条件') }}</span>
      </div>
      <div class="body" v-if="formInfo.buttonConfigs && formInfo.buttonConfigs.length > 0">
        <div class="row item" v-for="(item, index) in formInfo.buttonConfigs" :key="index">
          <span class="small"
            ><a-checkbox v-model:checked="item.checked" :disabled="getDisabled(item.code)"
          /></span>
          <span class="common">{{ item.buttonName }}</span>
          <span class="common">{{ item.buttonCode }}</span>
          <span class="big">
            <a-select
              v-if="item.buttonCode == 'reject'"
              v-model:value="item.buttonOpera"
              style="width: 100%"
            >
              <a-select-option
                v-for="(item2, index2) in rejectTypeOption"
                :key="index2"
                :value="item2.value"
                >{{ item2.label }}</a-select-option
              >
            </a-select>
            <template v-if="item.buttonType != ButtonType.DEFAULT">
              <Icon icon="clarity:note-edit-line" @click="editItem(index)" />
              <Icon
                icon="ant-design:delete-outlined"
                class="delete-icon"
                @click="deleteItem(index)"
              />
            </template>
          </span>
        </div>
      </div>
      <EmptyBox v-else :has-icon="false" />
    </div>
  </div>
  <ModalPanel
    :visible="button.visible"
    :width="600"
    :title="t('添加按钮')"
    @submit="submit"
    @close="close"
  >
    <div class="pl-3 pr-3">
      <FormItem required :label="t('按钮名称：')">
        <a-input
          v-model:value="button.settingConfig.buttonName"
          :placeholder="t('请填写按钮名称')"
        />
      </FormItem>
      <FormItem required :label="t('按钮编码：')">
        <a-input
          v-model:value="button.settingConfig.buttonCode"
          :placeholder="t('请填写按钮编码')"
        />
      </FormItem>
      <FormItem required :label="t('按钮类型：')">
        <a-radio-group v-model:value="button.settingConfig.buttonType" name="radioGroup">
          <a-radio v-for="(item, index) in buttonTypeList" :key="index" :value="item.value">{{
            item.label
          }}</a-radio>
        </a-radio-group>
      </FormItem>

      <template v-if="button.settingConfig.buttonType == ButtonType.SCRIPT">
        <FormItem :label="t('脚本格式：')" v-if="button.settingConfig.scriptLanguage != undefined">
          <a-select v-model:value="button.settingConfig.scriptLanguage" style="width: 100%">
            <a-select-option :value="0">JavaScript</a-select-option>
            <a-select-option :value="1">Groovy</a-select-option>
          </a-select>
        </FormItem>
        <FormItem :label="t('执行脚本：')" v-if="button.settingConfig.scriptLanguage != undefined">
          <a-textarea v-model:value="button.settingConfig.scriptContent" :maxlength="100" />
        </FormItem>
      </template>
      <FormItem
        required
        :label="t('Api配置：')"
        v-if="button.settingConfig.buttonType == ButtonType.API"
      >
        <ApiSelect style="width: 100%" v-model="button.settingConfig.apiConfig" />
      </FormItem>
    </div>
  </ModalPanel>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { ApproveCode, ApproveType, ButtonType, RejectType } from '/@/enums/workflowEnum';
  import { NodeHead } from '/@/components/ModalPanel/index';
  import Icon from '/@/components/Icon/index';
  import FormItem from '/@bpmn/layout/FormItem.vue';
  import { ModalPanel, EmptyBox } from '/@/components/ModalPanel/index';
  import ApiSelect from '/@bpmn/components/arguments/ApiSelect.vue';
  import { cloneDeep } from 'lodash-es';
  import { ButtonConfigItem } from '/@/model/workflow/workflowConfig';
  import { message } from 'ant-design-vue';
  import { useBpmnStore } from '/@bpmn/store/bpmn';
  import { storeToRefs } from 'pinia';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const store = useBpmnStore();
  const { infoId } = store;
  const { info } = storeToRefs(store);
  const formInfo = ref();

  formInfo.value = info.value.get(infoId);

  let button: {
    modelType: string;
    editIndex: number;
    visible: boolean;
    settingConfig: ButtonConfigItem;
  } = reactive({
    modelType: 'add',
    editIndex: -1,
    visible: false,
    settingConfig: {
      buttonType: ButtonType.SCRIPT, //按钮类型
      buttonName: '', //按钮名称
      buttonCode: '', //按钮编码
      checked: true, //选中
      approveType: ApproveType.OTHER,
      scriptLanguage: 0,
      scriptContent: '',
      apiConfig: {
        id: '',
        name: '',
        method: '',
        requestParamsConfigs: [], //Query Params
        requestHeaderConfigs: [], //Header
        requestBodyConfigs: [], //Body
      },
    },
  });
  const rejectTypeOption = [
    {
      value: RejectType.ALL,
      label: t('允许驳回至任一流转过的节点'),
    },
    {
      value: RejectType.ONLY,
      label: t('仅允许驳回至上一节点'),
    },
  ];
  const buttonTypeList = [
    {
      label: t('执行脚本'),
      value: ButtonType.SCRIPT,
    },
    {
      label: 'API',
      value: ButtonType.API,
    },
  ];

  function getDisabled(code: ApproveCode) {
    if ([ApproveCode.AGREE, ApproveCode.DISAGREE].includes(code)) {
      return true;
    }
    return false;
  }
  //删除
  function deleteItem(index: number) {
    formInfo.value.buttonConfigs.splice(index, 1);
  }

  function addItem() {
    button.modelType = 'add';
    button.settingConfig = {
      buttonType: ButtonType.SCRIPT, //按钮类型
      buttonName: '', //按钮名称
      buttonCode: '', //按钮编码
      checked: true, //选中
      approveType: ApproveType.OTHER,
      scriptLanguage: 0,
      scriptContent: '',
      apiConfig: {
        id: '',
        name: '',
        method: '',
        requestParamsConfigs: [], //Query Params
        requestHeaderConfigs: [], //Header
        requestBodyConfigs: [], //Body
      },
    };
    button.visible = true;
  }
  function editItem(index: number) {
    button.editIndex = index;
    button.modelType = 'edit';
    button.settingConfig = cloneDeep(formInfo.value.buttonConfigs[index]);
    button.visible = true;
  }
  function validate() {
    if (!button.settingConfig.buttonName) {
      message.error(t('请填写按钮名称'));
      return false;
    }
    if (!button.settingConfig.buttonCode) {
      message.error(t('请填写按钮编码'));
      return false;
    }
    return true;
  }
  function submit() {
    let validateVal = validate();
    if (validateVal === false) {
      return false;
    }
    let buttonCodes = formInfo.value.buttonConfigs.map((ele) => {
      return ele.buttonCode;
    });
    if (button.modelType == 'add') {
      if (!buttonCodes.includes(button.settingConfig.buttonCode)) {
        formInfo.value.buttonConfigs.push(button.settingConfig);
      } else {
        message.error(t('按钮编码 必须唯一'));
        return false;
      }
    } else if (button.modelType == 'edit') {
      if (
        formInfo.value.buttonConfigs[button.editIndex].buttonCode ==
          button.settingConfig.buttonCode ||
        !buttonCodes.includes(button.settingConfig.buttonCode)
      ) {
        formInfo.value.buttonConfigs[button.editIndex] = button.settingConfig;
      } else {
        message.error(t('按钮编码 必须唯一'));
        return false;
      }
    }
    close();
  }
  function close() {
    button.visible = false;
  }
</script>

<style lang="less" scoped>
  .list-box {
    .opr-box {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
  }

  .list {
    .row {
      height: 40px;
      line-height: 30px;
      display: flex;
      justify-content: space-around;
      align-items: center;

      span {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .common {
        flex-basis: 18%;
        margin-right: 4px;
      }

      .big {
        flex-basis: 70%;
      }

      .small {
        flex-basis: 16%;
      }
    }

    .head {
      background-color: #f9f9f9;
    }

    .item {
      border-bottom: 1px solid #f9f9f9;
    }

    .delete-icon {
      color: @clear-color;
    }
  }
</style>
