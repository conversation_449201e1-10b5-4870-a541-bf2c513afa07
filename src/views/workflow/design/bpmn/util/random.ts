// 随机数字
export function randomStr() {
  return Math.random().toString(36).slice(-8);
}
// 随机表单字符串
export function randomFormNameStr() {
  return Math.random().toString(36).slice(-4);
}
// 时间戳随机数
export function randomTime() {
  return new Date().getTime();
}
// 随机字母+时间戳随机数
export function randomNum() {
  //创建26个字母数组
  const arr = [
    'a',
    'b',
    'c',
    'd',
    'e',
    'f',
    'g',
    'h',
    'i',
    'j',
    'k',
    'l',
    'm',
    'n',
    'o',
    'p',
    'q',
    'r',
    's',
    't',
    'u',
    'v',
    'w',
    'x',
    'y',
    'z',
  ];
  let idvalue = '';
  const n = 8; //这个值可以改变的，对应的生成多少个字母，根据自己需求所改
  for (let i = 0; i < n; i++) {
    idvalue += arr[Math.floor(Math.random() * 26)];
  }

  return idvalue + randomTime();
}
