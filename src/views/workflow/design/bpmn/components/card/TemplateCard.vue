<template>
  <div class="list-item">
    <div class="item-box">
      <div class="item-left">
        <div class="icon-box"> <IconFontSymbol icon="shenpi" fill-color="#5e95ff" /></div
      ></div>
      <div class="item-right">
        <div class="template-box">
          <!-- <span class="item-title">{{ t('编码') }}</span> -->
          <span class="item-form-name">{{ props.item.code }}</span>
        </div>
        <div class="template-box">
          <!-- <span class="item-title">{{ t('名称') }}</span> -->
          <span class="item-title">{{ props.item.name }}</span>
        </div>
      </div>
      <div class="fixed-checked">
        <slot name="pick"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import IconFontSymbol from '/@/components/IconFontSymbol/Index.vue';
  interface Obj {
    name: string;
    code: string;
  }
  let props = withDefaults(defineProps<{ item: Obj }>(), {
    item: () => {
      return { name: '', code: '' };
    },
  });
</script>

<style lang="less" scoped>
  @custom-color: #5e95ff;
  @bg-color: #ffffff;

  .list-item {
    width: 258px;
    height: 100px;
    background: @bg-color;
    border-color: @custom-color;
    border-radius: 8px;
    margin: 1%;
    overflow: hidden;
    box-sizing: border-box;
    border: 1px solid #f3f3f3;

    &:hover {
      border: 1px solid @custom-color;
    }

    .item-box {
      display: flex;
      margin: 14px;
      position: relative;
      height: 62px;
      align-items: center;

      .item-left {
        margin-right: 20px;

        .icon-box {
          width: 60px;
          height: 60px;
          background: rgb(94 149 255 / 8%);
          border-radius: 10px;
          font-size: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .item-right {
        font-size: 14px;
        width: 100%;
        z-index: 9;
      }

      .fixed-checked {
        position: absolute;
        bottom: -20px;
        z-index: 1;
        right: -6px;
      }

      .fixed-icon {
        position: absolute;
        right: -44px;
        font-size: 99px;
        transform: rotate(-30deg);
        top: -44px;
        z-index: 0;
      }
    }
  }

  .template-box {
    display: flex;
    margin: 4px 0;
    width: 100%;
  }

  .img-box {
    width: 100%;
    height: 100%;
  }

  :deep(.ant-checkbox-inner) {
    border-color: @custom-color;
  }

  :deep(.ant-checkbox-checked .ant-checkbox-inner) {
    background-color: @custom-color;
  }

  :deep(.ant-checkbox-checked::after),
  :deep(.ant-checkbox-wrapper:hover .ant-checkbox-inner, .ant-checkbox:hover),
  :deep(.ant-checkbox-inner),
  :deep(.ant-checkbox:hover),
  :deep(.ant-checkbox-input:focus + .ant-checkbox-inner) {
    border-color: @custom-color;
  }

  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #636e80;
  }

  .item-form-name {
    font-weight: bold;
    font-size: 16px;
    color: #262626;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
