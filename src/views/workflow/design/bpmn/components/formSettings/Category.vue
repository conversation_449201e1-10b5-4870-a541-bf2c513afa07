<template>
  <div class="form-category">
    <NodeHead class="title" :node-name="t('表单类型')" />
    <div class="tree-box">
      <div
        class="item"
        :class="props.modelValue == FormType.WORKFLOW ? 'checked' : ''"
        @click="change(FormType.WORKFLOW)"
        v-if="isShowAdded"
      >
        <IconFontSymbol class="item-icon" icon="xitong" />
        <span class="item-text">{{ t('流程已添加表单') }}</span>
      </div>
      <div
        class="item"
        :class="props.modelValue == FormType.CUSTOM ? 'checked' : ''"
        @click="change(FormType.CUSTOM)"
      >
        <IconFontSymbol class="item-icon" icon="zidingyi" />
        <span class="item-text">{{ t('自定义表单') }}</span>
      </div>
      <div
        class="item"
        :class="props.modelValue == FormType.SYSTEM ? 'checked' : ''"
        @click="change(FormType.SYSTEM)"
      >
        <IconFontSymbol class="item-icon" icon="xitong" />
        <span class="item-text">{{ t('系统表单') }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { FormType } from '/@/enums/workflowEnum';
  import { NodeHead } from '/@/components/ModalPanel/index';
  import IconFontSymbol from '/@/components/IconFontSymbol/Index.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const emit = defineEmits(['update:modelValue', 'change']);
  const props = defineProps({
    modelValue: Number,
    isShowAdded: {
      type: Boolean,
      default: true,
    },
  });
  function change(type: FormType) {
    emit('update:modelValue', type);
    emit('change');
  }
</script>

<style lang="less" scoped>
  .form-category {
    .title {
      height: 40px;
      font-size: 16px;
      color: #333;
      border-bottom: 1px solid #f0f0f0;
    }

    .tree-box {
      padding: 10px 0;

      .item {
        display: flex;
        align-items: center;
        height: 40px;
        font-size: 14px;
        color: #606266;
        padding: 10px;

        .item-icon {
          font-size: 14px;
          color: #606266;
        }

        .item-text {
          margin-left: 20px;
        }
      }

      .checked {
        background: #e5f1ff;
      }
    }
  }
</style>
