<template>
  <SelectApiConfig
    v-model="config"
    :paramTree="ProcessDataVariable"
    :exampleStr="exampleStr"
    @update:modelValue="submit"
  >
    <slot></slot>
  </SelectApiConfig>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { SelectApiConfig } from '/@/components/ApiConfig';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { ApiConfig } from '/@/components/ApiConfig/src/interface';
  import { ProcessDataVariable } from '/@bpmn/config/rules';
  import { MemberConfig } from '/@/model/workflow/memberSetting';
  import { MemberType } from '/@/enums/workflowEnum';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const emit = defineEmits(['change']);
  let props = withDefaults(defineProps<{ memberList: Array<MemberConfig> }>(), {
    memberList: () => {
      return [];
    },
  });
  let config = ref<ApiConfig>();
  const exampleStr = `{
  code: 0,
  msg: 'success',
  data: '1830999999983899309,2835993035383895389',  //用户ID，可以多个，用逗号分隔
}`;
  const memberType = MemberType.API;
  const { notification } = useMessage();
  function submit() {
    let list: Array<MemberConfig> = [];
    if (props.memberList.length > 0) {
      list = props.memberList.filter((ele: MemberConfig) => {
        if (ele.memberType != memberType) return ele;
      });
    }
    if (config.value?.path) {
      list.push({
        memberType: memberType,
        id: config.value.id,
        name: config.value.path,
        apiConfig: config.value,
      });
      emit('change', [...list]);
    } else {
      notification.warning({
        message: t('提示'),
        description: t('您没有选择API'),
      });
    }
  }
</script>
