<template>
  <div class="list-box">
    <div class="opr-box">
      <div class="header-box"><NodeHead :nodeName="t('人员列表')" /></div>
      <div class="button-box">
        <div v-for="(item, index) in allComponentList" :key="index">
          <component
            v-if="item.show"
            :is="item.component"
            :memberList="props.memberList"
            @change="changeList"
          >
            <a-button type="primary">{{ item.name }}</a-button>
          </component>
        </div>
      </div>
    </div>

    <a-table :dataSource="props.memberList" :columns="configColumns" :pagination="false">
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'memberType'">
          {{ getMemberType(record.memberType) }}
        </template>
        <template v-if="column.key === 'operation'">
          <Icon icon="ant-design:delete-outlined" class="delete-icon" @click="deleteItem(index)" />
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
  import { NodeHead } from '/@/components/ModalPanel/index';
  import Icon from '/@/components/Icon/index';
  import Posts from '/@bpmn/components/member/Posts.vue';
  import Roles from '/@bpmn/components/member/Roles.vue';
  import Users from '/@bpmn/components/member/Users.vue';
  import NodeApprover from '/@bpmn/components/member/NodeApprover.vue';
  import UpperManagement from '/@bpmn/components/member/UpperManagement.vue';
  import FormFields from '/@bpmn/components/member/FormFields.vue';

  import { MemberConfig } from '/@/model/workflow/memberSetting';
  import { computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import ApiSelect from '/@bpmn/components/member/ApiSelect.vue';
  import ApprovalUser from '/@bpmn/components/member/ApprovalUser.vue';
  import { getMemberType } from '../../config/info';

  const { t } = useI18n();
  const emits = defineEmits(['update:memberList']);
  const props = withDefaults(
    defineProps<{
      isCommonType: Boolean;
      isUpper?: Boolean;
      isApiApprover?: Boolean;
      memberList: Array<MemberConfig>;
    }>(),
    {
      isCommonType: () => false,
      isUpper: () => false,
      isApiApprover: () => false,
      memberList: () => {
        return [];
      },
    },
  );
  const configColumns = [
    {
      title: t('类型'),
      dataIndex: 'memberType',
      key: 'memberType',
      align: 'center',
    },
    {
      title: t('名称'),
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: t('操作'),
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
    },
  ];

  const allComponentList = computed(() => {
    return [
      { name: t('添加岗位'), component: Posts, show: true },
      { name: t('添加角色'), component: Roles, show: true },
      { name: t('添加人员'), component: Users, show: true },
      { name: t('节点审批人'), component: NodeApprover, show: props.isCommonType ? false : true },
      {
        name: t('上级领导'),
        component: UpperManagement,
        show: props.isCommonType ? false : props.isUpper ? false : true,
      },
      { name: t('表单字段'), component: FormFields, show: props.isCommonType ? false : true },
      { name: t('API审批人'), component: ApiSelect, show: props.isApiApprover ? true : false },
      { name: '审批专员', component: ApprovalUser, show: true },
    ];
  });
  // 类型

  function changeList(list: Array<MemberConfig>) {
    emits('update:memberList', list);
  }
  function deleteItem(index: number) {
    let list = props.memberList;
    list.splice(index, 1);
    emits('update:memberList', list);
  }
</script>

<style lang="less" scoped>
  .list-box {
    .opr-box {
      display: flex;
      justify-content: space-between;
      margin-bottom: 6px;

      .header-box {
        flex-basis: 220px;
        align-items: flex-start;
        margin-bottom: -10px;
      }
    }

    .button-box {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      :deep(button) {
        margin: 4px;
        width: 88px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .delete-icon {
    color: @clear-color;
  }
</style>
