<template>
  <span @click.stop="show">
    <slot></slot>
    <a-modal
      v-model:visible="data.visible"
      :width="800"
      :title="t('批量审批')"
      @ok="submit"
      @cancel="cancel"
      :okText="t('确定')"
      :cancelText="t('取消')"
    >
      <div class="model-box">
        <NodeHead :nodeName="t('审批流程')" />
        <a-table
          class="box"
          :pagination="false"
          :dataSource="props.selectedRows"
          :columns="configColumns"
          :scroll="{ y: '160px' }"
        />
        <div class="mt-2 mb-2">
          <NodeHead :nodeName="t('审批信息')" />
          <div class="text-box">
            <div class="text-label">{{ t('审批结果：') }}</div>
            <a-radio-group v-model:value="data.approvedType" name="approvedType" class="flex-1">
              <a-radio :value="ApproveType.AGREE">{{ t('同意') }}</a-radio>
              <a-radio :value="ApproveType.DISAGREE">{{ t('拒绝') }}</a-radio>
            </a-radio-group>
          </div>
          <div class="text-box">
            <div class="text-label">{{ t('审批内容：') }}</div>
            <a-textarea
              v-model:value="data.approvedContent"
              :rows="6"
              :maxlength="100"
              class="flex-1"
            />
          </div>
        </div>
        <div v-if="needStampRef" class="mt-2 mb-2">
          <NodeHead :nodeName="t('签章')" />
          <a-form :model="data" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }" ref="formData">
            <!-- 电子签章 -->
            <a-form-item
              :label="t('电子签章')"
              name="stampId"
              :rules="[{ required: true, message: t('请选择电子签章') }]"
              v-if="data.hasStamp"
            >
              <SelectStamp v-if="data.hasStamp" v-model:stampId="data.stampId" />
            </a-form-item>
            <a-form-item
              :label="t('签章密码')"
              name="password"
              :rules="[{ required: true, message: t('请输入签章密码') }]"
              v-if="data.hasStampPassword"
            >
              <a-input-password v-model:value="data.password" />
            </a-form-item>
          </a-form>
        </div>
      </div>
    </a-modal>
  </span>
</template>

<script setup lang="ts">
  import { reactive, ref } from 'vue';
  import { NodeHead } from '/@/components/ModalPanel/index';
  import { TasksModel } from '/@/api/workflow/model';
  import { ApproveType } from '/@/enums/workflowEnum';
  import { getBatchApprovalInfo, postBatchApproval } from '/@/api/workflow/task';
  import SelectStamp from './stamp/SelectStamp.vue';
  import { GetBatchApprovalInfo, PostBatchApprovalData } from '/@/model/workflow/bpmnConfig';
  import { message } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      selectedRows: Array<TasksModel>;
    }>(),
    {
      selectedRows: () => {
        return [];
      },
    },
  );
  let emits = defineEmits(['close']);
  const formData = ref();
  const configColumns = [
    {
      title: '序号',
      align: 'center',
      customRender: ({ index }) => `${index + 1}`, // 显示每一行的序号
      width: 80,
    },
    {
      title: t('流水号'),
      dataIndex: 'serialNumber',
      sorter: {
        multiple: 1,
      },
    },
    {
      title: t('流程名称'),
      dataIndex: 'processName',
      sorter: {
        multiple: 2,
      },
    },
    {
      title: t('当前任务名称'),
      dataIndex: 'taskName',
      width: 160,
      sorter: {
        multiple: 3,
      },
    },
    {
      title: t('流程发起人'),
      dataIndex: 'startUserName',
    },
  ];

  const data: {
    visible: boolean;
    hasStamp: boolean;
    hasStampPassword: boolean;
    stampId: string;
    password: string;
    approvedType: ApproveType;
    approvedContent: string;
    dataSource: Array<TasksModel>;
  } = reactive({
    visible: false,
    hasStamp: true,
    hasStampPassword: true,
    stampId: '',
    password: '',
    dataSource: [],
    approvedType: ApproveType.AGREE,
    approvedContent: '',
  });

  const needStampRef = ref(true);

  async function show() {
    data.visible = true;
    let ids = props.selectedRows
      .map((ele) => {
        return ele.taskId;
      })
      .join(',');
    const param: GetBatchApprovalInfo = { taskIds: ids };
    const info = await getBatchApprovalInfo(param);
    needStampRef.value = info.needStamp;
    data.hasStampPassword = info.needPassword ? true : false;
  }

  async function submit() {
    let ids: Array<string> = props.selectedRows.map((ele) => {
      return ele.taskId;
    });
    let params: PostBatchApprovalData = {
      approvedContent: data.approvedContent,
      approvedType: data.approvedType,
      stampId: data.stampId,
      taskIds: ids,
    };
    if (data.password) {
      params.stampPassword = data.password;
    }
    try {
      if (needStampRef.value) {
        await formData.value.validate();
      }
      let res = await postBatchApproval(params);
      if (res) {
        message.success(t('批量审批成功'));
        data.visible = false;
        emits('close', res);
      } else {
        message.error(t('批量审批失败'));
      }
    } catch (error) {}
  }
  function cancel() {
    data.visible = false;
    emits('close');
  }
</script>

<style lang="less" scoped>
  .model-box {
    padding: 10px 20px;
  }

  .text-box {
    display: flex;
    margin: 10px 0;

    .text-label {
      width: 80px;
      display: inline-flex;
      justify-content: flex-end;
      margin-right: 4px;
    }
  }
</style>
