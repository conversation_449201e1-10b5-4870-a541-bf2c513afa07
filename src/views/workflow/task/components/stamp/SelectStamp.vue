<template>
  <a-input-group compact class="box" @click.stop="">
    <a-select
      :value="props.stampId"
      :placeholder="t('请选择电子签章')"
      allowClear
      style="width: calc(100% - 48px)"
      @change="changeIds"
    >
      <a-select-option :value="bind.id" v-for="bind in data.list" :key="bind.id">
        {{ bind.name }}
      </a-select-option>
    </a-select>
    <a-button class="fixed" @click.stop="data.visible = true">
      <Icon icon="ant-design:plus-outlined"
    /></a-button>
    <StampDetail v-if="data.visible" :type="StampType.PRIVATE_SIGNATURE" @close="close" />
  </a-input-group>
</template>

<script setup lang="ts">
  import { reactive, onMounted } from 'vue';
  import { Icon } from '/@/components/Icon';
  import StampDetail from './StampInfo.vue';
  import { getStampPage } from '/@/api/workflow/stamp';
  import { StampInfo } from '/@/api/workflow/model';
  import { StampType } from '/@/enums/workflowEnum';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  let emits = defineEmits(['update:stampId']);
  const props = defineProps({
    stampId: String,
  });

  let data: { visible: boolean; list: Array<StampInfo> } = reactive({
    visible: false,
    list: [],
  });
  onMounted(async () => {
    await getList();
  });
  function close() {
    data.visible = false;
    getList();
  }
  async function getList() {
    let options = await getStampPage(StampType.PRIVATE_SIGNATURE, {
      limit: 1,
      size: 100,
    });
    data.list = options.list;
    data.list.forEach((o) => {
      if (o.isDefault === 1) {
        emits('update:stampId', o.id);
      }
    });
  }
  function changeIds(val) {
    console.log('val: ', val);
    emits('update:stampId', val);
  }
</script>

<style scoped>
  .fixed {
    font-weight: 700;
    width: 49px;
  }
</style>
