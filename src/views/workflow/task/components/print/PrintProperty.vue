<template>
  <div>
    <NodeHead :nodeName="t('打印属性')" />
    <a-form
      :model="printData"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 16 }"
      @finish="printForm"
    >
      <a-form-item :label="t('边框颜色')" name="borderColor">
        <ColorPicker v-model:value="printData.borderColor" />
      </a-form-item>
      <a-form-item :label="t('页面风格')" name="style">
        <a-select v-model:value="printData.style" style="width: 100%">
          <a-select-option value="1">默认风格</a-select-option>
          <a-select-option value="2">公文风格</a-select-option>
          <a-select-option value="3">发文稿纸风格</a-select-option>
          <a-select-option value="4">下划线风格</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="标题下划线" name="underline">
        <a-select v-model:value="printData.underline" style="width: 100%">
          <a-select-option value="1">是，标题带有下划线</a-select-option>
          <a-select-option value="0">否，标题不带下划线</a-select-option>
        </a-select>
      </a-form-item>
      <div class="button-box">
        <a-button
          type="primary"
          html-type="submit"
          class="mr-2"
          :loading="printData.submitLoading"
          >{{ t('打印当前页表单') }}</a-button
        >
      </div>
    </a-form>
    <FormPrint
      v-if="isShowPrint"
      v-model:isShowPrint="isShowPrint"
      :formConfigs="formConfigs"
      :printData="printData"
    />
  </div>
</template>
<script setup lang="ts">
  import { ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import FormPrint from './FormPrint.vue';
  import { ColorPicker } from '/@/components/ColorPicker';
  import { NodeHead } from '/@/components/ModalPanel/index';
  const { t } = useI18n();
  const printData = ref({
    borderColor: '#000000',
    style: '1',
    underline: '0',
    submitLoading: false,
  });
  const isShowPrint = ref<boolean>(false);
  defineProps({
    tabKey: String,
    formConfigs: Object,
  });

  function printForm() {
    isShowPrint.value = true;
  }
</script>
