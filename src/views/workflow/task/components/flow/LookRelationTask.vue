<template>
  <FlowPanel
    v-if="visible"
    :tab-position="position ? position : 'top'"
    :xml="data.xml"
    :taskRecords="data.taskRecords"
    :predecessorTasks="[]"
    :processId="props.processId"
    position="top"
  >
    <FormInformation
      :opinionsComponents="data.opinionsComponents"
      :opinions="data.opinions"
      :formInfos="data.formInfos"
      :disabled="true"
    />
  </FlowPanel>
</template>

<script setup lang="ts">
  import FormInformation from './FormInformation.vue';
  import FlowPanel from './FlowPanel.vue';
  import { getRelationTaskInfo } from '/@/api/workflow/task';
  import { onMounted, ref } from 'vue';
  import userTaskItem from './../../hooks/userTaskItem';
  let props = defineProps(['taskId', 'position', 'processId']);
  let visible = ref(false);
  const { data, initProcessData } = userTaskItem();
  onMounted(async () => {
    try {
      let res = await getRelationTaskInfo(props.taskId);
      initProcessData(res);
      visible.value = true;
    } catch (error) {}
  });
</script>
<style lang="less" scoped></style>
