<template>
  <div>
    <!-- 附件汇总 -->
    <EmptyBox
      v-if="data.dataSource.length == 0"
      :title="t('当前无相关附件记录')"
      :desc="t('流程需发起并在表单中上传附件才会有附件记录产生')"
    />
    <a-table
      v-else
      class="p-4"
      :pagination="false"
      :dataSource="data.dataSource"
      :columns="configColumns"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'components'"> {{ t('上传') }} </template>
        <template v-if="column.dataIndex === 'operation'">
          <div class="flex">
            <a-button size="small" type="primary" class="mr-2" @click="preview(record.fileUrl)">{{
              t('预览')
            }}</a-button>
            <a-button
              size="small"
              type="primary"
              @click="download(record.fileUrl, record.fileName + record.fileType)"
              >{{ t('下载') }}</a-button
            >
          </div>
        </template>
      </template>
    </a-table>
    <a-modal
      v-model:visible="data.showPreview"
      :title="t('预览文件')"
      width="100%"
      wrap-class-name="full-modal"
      :footer="null"
      @ok="data.showPreview = false"
    >
      <iframe v-if="data.showPreview" :src="data.fileUrl" class="iframe-box"></iframe>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, reactive } from 'vue';
  import EmptyBox from './EmptyBox.vue';
  import { getFileList } from '/@/api/system/file';
  import { FilePageListModel } from '/@/api/system/file/model';
  import { downloadByUrl } from '/@/utils/file/download';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getAppEnvConfig } from '/@/utils/env';
  const { t } = useI18n();
  const props = withDefaults(defineProps<{ processId: string }>(), {
    processId: '',
  });

  const configColumns = [
    {
      title: t('序号'),
      align: 'center',
      customRender: ({ index }) => `${index + 1}`, // 显示每一行的序号
      width: 80,
    },
    {
      title: t('附件名称'),
      dataIndex: 'fileName',
      sorter: {
        multiple: 4,
      },
    },
    {
      title: t('附件格式'),
      dataIndex: 'fileType',
    },
    {
      title: t('所属组件'),
      dataIndex: 'components',
    },
    {
      title: t('上传人员'),
      dataIndex: 'createUserName',
    },
    {
      title: t('上传时间'),
      dataIndex: 'createDate',
    },
    {
      title: t('操作'),
      dataIndex: 'operation',
      width: 120,
      align: 'center',
    },
  ];
  const data: {
    dataSource: Array<FilePageListModel>;
    showPreview: boolean;
    fileUrl: string;
  } = reactive({
    dataSource: [],
    showPreview: false,
    fileUrl: '',
  });
  onMounted(async () => {
    if (props.processId) {
      try {
        let res = await getFileList({ processId: props.processId });
        data.dataSource = res;
      } catch (error) {}
    }
  });
  async function preview(fileUrl: string) {
    data.fileUrl = getAppEnvConfig().VITE_GLOB_UPLOAD_PREVIEW + encodeURIComponent(encode(fileUrl));
    data.showPreview = true;
  }
  async function download(fileUrl: string, fileName: string) {
    downloadByUrl({
      url: fileUrl,
      fileName: fileName,
    });
  }

  function encode(input: string) {
    let _keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
    let output = '';
    let chr1, chr2, chr3, enc1, enc2, enc3, enc4;
    let i = 0;
    input = utf8_encode(input);
    while (i < input.length) {
      chr1 = input.charCodeAt(i++);
      chr2 = input.charCodeAt(i++);
      chr3 = input.charCodeAt(i++);
      enc1 = chr1 >> 2;
      enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
      enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
      enc4 = chr3 & 63;
      if (isNaN(chr2)) {
        enc3 = enc4 = 64;
      } else if (isNaN(chr3)) {
        enc4 = 64;
      }
      output =
        output +
        _keyStr.charAt(enc1) +
        _keyStr.charAt(enc2) +
        _keyStr.charAt(enc3) +
        _keyStr.charAt(enc4);
    }
    return output;
  }

  function utf8_encode(input: string) {
    input = input.replace(/\r\n/g, '\n');
    let tufters = '';
    for (let n = 0; n < input.length; n++) {
      let c = input.charCodeAt(n);
      if (c < 128) {
        tufters += String.fromCharCode(c);
      } else if (c > 127 && c < 2048) {
        tufters += String.fromCharCode((c >> 6) | 192);
        tufters += String.fromCharCode((c & 63) | 128);
      } else {
        tufters += String.fromCharCode((c >> 12) | 224);
        tufters += String.fromCharCode(((c >> 6) & 63) | 128);
        tufters += String.fromCharCode((c & 63) | 128);
      }
    }
    return tufters;
  }
</script>

<style lang="less" scoped>
  .iframe-box {
    width: 100%;
    height: 80vh;
    padding: 20px;
  }

  .full-modal {
    .ant-modal {
      max-width: 100%;
      top: 0;
      padding-bottom: 0;
      margin: 0;
    }

    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: 80vh;
    }

    .ant-modal-body {
      flex: 1;
    }
  }
</style>
