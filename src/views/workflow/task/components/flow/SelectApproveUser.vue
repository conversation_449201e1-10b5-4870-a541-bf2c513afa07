<template>
  <div v-if="data.visible">
    <a-tabs>
      <a-tab-pane key="1" :tab="t('候选人')">
        <a-button @click="checkAll()" class="mr-2">
          {{ data.isCheckAll ? '全选' : '反选' }}
        </a-button>
        <div class="list-page-box" v-if="data.approvedList.length > 0">
          <UserCard
            :class="data.approvedIds.includes(user.id) ? 'picked' : 'not-picked'"
            v-for="(user, userIndex) in data.approvedList"
            :key="userIndex"
            :item="user"
            @click="checkApprovedId(user)"
            :disabled="user.canRemove ? false : true"
          >
            <template #check>
              <a-checkbox
                size="small"
                :checked="data.approvedIds.includes(user.id)"
                :disabled="user.canRemove ? false : true"
              />
            </template>
          </UserCard>
        </div>
      </a-tab-pane>

      <a-tab-pane key="2" :tab="t('已选人员')">
        <SelectUser
          v-if="hasMoreBtn"
          :selectedIds="data.selectedIds"
          :disabledIds="data.disabledIds"
          :multiple="true"
          @change="changeList"
        >
          <a-button type="primary">{{ t('更多人员添加') }}</a-button>
        </SelectUser>
        <div class="list-page-box" v-if="data.selectedList.length > 0">
          <UserCard
            :class="data.selectedIds.includes(user.id) ? 'picked' : 'not-picked'"
            v-for="(user, userIndex) in data.selectedList"
            :key="userIndex"
            :item="user"
            @click="checked(user)"
            :disabled="data.disabledIds.includes(user.id)"
          >
            <template #check>
              <a-checkbox size="small" :checked="data.selectedIds.includes(user.id)" />
            </template>
          </UserCard>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, reactive } from 'vue';
  import { getUserMulti } from '/@/api/system/user';
  import { getApproveUserList } from '/@/api/workflow/task';
  import { SelectUser } from '/@/components/SelectOrganizational/index';
  import { UserCard } from '/@/components/SelectOrganizational/index';
  import { cloneDeep } from 'lodash-es';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = defineProps({
    schemaId: String,
    taskId: String,
    hasMoreBtn: {
      type: Boolean,
      default: true,
    },
  });
  const emits = defineEmits(['update:selectIds']);
  // const emits = defineEmits('change');
  let data: {
    visible: boolean;
    approvedList: Array<{
      [x: string]: any;
      id: string;
      name: string;
    }>;
    selectedList: Array<{ id: string; name: string }>;
    approvedIds: Array<string>;
    disabledIds: Array<string>;
    selectedIds: Array<string>;
    isCheckAll: Boolean;
  } = reactive({
    visible: false,
    approvedList: [],
    selectedList: [],
    approvedIds: [],
    disabledIds: [],
    selectedIds: [],
    isCheckAll: false,
  });
  onMounted(async () => {
    if (props.schemaId && props.taskId) {
      try {
        let userList = await getApproveUserList(props.schemaId, props.taskId);
        data.approvedList = cloneDeep(userList);
        data.approvedIds = data.approvedList.map((ele) => {
          return ele.id;
        });
        data.disabledIds = data.approvedList
          .filter((ele) => {
            return !ele.canRemove;
          })
          .map((ele) => {
            return ele.id;
          });

        data.selectedList = cloneDeep(userList);
        data.selectedIds = data.selectedList.map((ele) => {
          return ele.id;
        });
        changeData();
        data.visible = true;
      } catch (_error) {}
    }
  });
  function checkAll() {
    if (data.isCheckAll) {
      let list = cloneDeep(data.approvedList);
      let approvedIds = list.map((ele) => {
        return ele.id;
      });
      changeList(approvedIds);
    } else {
      changeList([]);
      data.approvedIds = [];
    }
    data.isCheckAll = !data.isCheckAll;
  }
  function checkApprovedId(user) {
    if (data.disabledIds.includes(user.id)) {
      return false;
    }
    if (data.approvedIds.includes(user.id)) {
      data.approvedIds.splice(
        data.approvedIds.findIndex((item) => item === user.id),
        1,
      );
      data.selectedIds.splice(
        data.selectedIds.findIndex((item) => item === user.id),
        1,
      );
      data.selectedList.splice(
        data.selectedList.findIndex((item) => item.id === user.id),
        1,
      );
    } else {
      data.approvedIds.push(user.id);
      data.selectedIds.push(user.id);
      data.selectedList.push(user);
    }
    changeData();
  }
  function checked(user) {
    if (data.disabledIds.includes(user.id)) {
      return false;
    }

    if (data.selectedIds.includes(user.id)) {
      data.selectedList.splice(
        data.selectedList.findIndex((item) => item.id === user.id),
        1,
      );
      data.selectedIds = data.selectedIds.filter((o) => {
        return o != user.id;
      });
    } else {
      data.selectedList.push(user);
      data.selectedIds.push(user.id);
    }

    if (data.approvedIds.includes(user.id)) {
      data.approvedIds.splice(
        data.approvedIds.findIndex((item) => item === user.id),
        1,
      );
    } else {
      data.approvedIds.push(user.id);
    }
    changeData();
  }
  async function changeList(userIds: Array<string>) {
    data.selectedList = await getUserMulti(userIds.join(','));
    data.selectedIds = userIds;
    userIds.forEach((id) => {
      if (!data.approvedIds.includes(id)) {
        data.approvedIds.push(id);
      }
    });
    changeData();
  }
  function changeData() {
    emits('update:selectIds', data.selectedIds);
  }
</script>

<style lang="less" scoped>
  .box {
    height: 500px;
  }

  .list-page-box {
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto;
    padding: 10px 0;
  }
</style>
