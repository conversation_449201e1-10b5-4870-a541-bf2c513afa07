<template>
  <div>
    <SelectUser :selectedIds="[]" :multiple="false" @change="submit">
      <a-button type="primary" style="width: 100%" class="mr-2">{{ t('转办') }}</a-button>
    </SelectUser>
  </div>
</template>

<script setup lang="ts">
  import { SelectUser } from '/@/components/SelectOrganizational/index';
  import { postTransfer } from '/@/api/workflow/task';
  import { notification } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = withDefaults(defineProps<{ taskId: string | undefined }>(), {
    taskId: '',
  });
  const emits = defineEmits(['close']);
  async function submit(ids: Array<string>) {
    if (ids.length === 1) {
      try {
        let res = await postTransfer(props.taskId, ids[0]);
        if (res) {
          notification.open({
            type: 'success',
            message: t('转办'),
            description: t('转办成功'),
          });
          emits('close');
        }
      } catch (error) {
        notification.open({
          type: 'error',
          message: t('转办'),
          description: t('转办失败：') + error,
        });
      }
    } else {
      notification.open({
        type: 'error',
        message: t('转办'),
        description: t('转办失败'),
      });
    }
  }
</script>
