<template>
  <FlowPanel
    v-if="visible"
    :tab-position="position ? position : 'top'"
    :xml="data.xml"
    :taskRecords="data.taskRecords"
    :predecessorTasks="[]"
    :processId="props.processId"
    position="top"
  >
    <FormInformation
      :opinionsComponents="data.opinionsComponents"
      :opinions="data.opinions"
      :formInfos="data.formInfos"
      :disabled="true"
      @get-form-configs="(config) => emits('getFormConfigs', config)"
    />
  </FlowPanel>
</template>

<script setup lang="ts">
  import FormInformation from './FormInformation.vue';
  import FlowPanel from './FlowPanel.vue';
  import { getApprovalProcess } from '/@/api/workflow/task';
  import { onMounted, ref } from 'vue';
  import userTaskItem from './../../hooks/userTaskItem';
  const emits = defineEmits(['getFormConfigs']);
  let props = defineProps(['position', 'processId', 'taskId']);
  let visible = ref(false);
  const { data, initProcessData } = userTaskItem();
  onMounted(async () => {
    try {
      let res = await getApprovalProcess(props.taskId, props.processId);
      initProcessData(res);
      visible.value = true;
    } catch (error) {}
  });
</script>
<style lang="less" scoped></style>
