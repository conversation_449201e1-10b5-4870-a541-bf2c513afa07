<template>
  <BasicTable @register="registerTable">
    <template #action="{ record }">
      <TableAction
        :actions="[
          {
            icon: 'chehui|svg',
            auth: 'processtasks:withdraw',
            tooltip: '撤回',
            onClick: handleWithdraw.bind(null, record),
          },
          {
            icon: 'ant-design:eye-outlined',
            auth: 'processtasks:view',
            tooltip: '查看',
            onClick: handleView.bind(null, record),
          },
          {
            icon: 'cuiban|svg',
            auth: 'processtasks:urge',
            tooltip: '催办',
            onClick: handleUrge.bind(null, record),
          },
        ]"
      />
    </template>
    <template #currentProgress="{ record }">
      <a-progress
        v-if="typeof record.currentProgress === 'number' && isFinite(record.currentProgress)"
        :percent="record.currentProgress"
        size="small"
      />
    </template>
  </BasicTable>
  <LaunchProcess
    v-if="restartProcessVisible"
    :schemaId="schemaIdProcess"
    :taskId="taskIdProcess"
    :isWithdraw="true"
    @close="restartProcessClose"
  />
  <LookProcess
    v-if="visibleLookProcess"
    :visible="visibleLookProcess"
    :taskId="taskIdProcess"
    :processId="processIdProcess"
    @close="visibleLookProcess = false"
  />
  <RejectProcess
    v-if="visibleRejectProcess"
    :visible="visibleRejectProcess"
    :taskId="taskIdProcess"
    :processId="processIdProcess"
    @close="handleRejectClose"
    @restart="restartProcess"
  />
  <UrgeProcess
    v-if="visibleUrgeProcess"
    :visible="visibleUrgeProcess"
    :taskId="taskIdProcess"
    :processId="processIdProcess"
    :status="urgeStatus"
    @close="handleUrgeClose"
  />
</template>

<script setup lang="ts">
  import userTaskTable from './../../hooks/userTaskTable';
  import { ref, h } from 'vue';

  import LookProcess from './../LookProcess.vue';
  import LaunchProcess from './../LaunchProcess.vue';
  import RejectProcess from './../RejectProcess.vue';
  import UrgeProcess from './../UrgeProcess.vue';

  import { BasicTable, useTable, BasicColumn, TableAction } from '/@/components/Table';
  import { getSchemaTask } from '/@/api/workflow/process';
  import { TaskTypeUrl } from '/@/enums/workflowEnum';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();
  const restartProcessVisible = ref(false);
  const configColumns: BasicColumn[] = [
    {
      title: t('流水号'),
      dataIndex: 'serialNumber',
      width: 70,
    },
    {
      title: t('流程名称'),
      dataIndex: 'processName',
      width: 200,
      align: 'left',
    },
    {
      title: t('任务名称'),
      dataIndex: 'currentTaskName',
      align: 'left',
    },
    {
      title: t('当前任务耗时'),
      dataIndex: 'consumingTime',
      align: 'left',
      width: 150,
      customRender: ({ record }) => {
        const color = record.consumingTime === '审批结束' ? '#b0f323' : '';
        return h('span', { style: { color } }, record.consumingTime);
      },
    },
    {
      title: t('流程状态'),
      dataIndex: 'status',
      align: 'left',
      width: 130,
      customRender: ({ record }) => {
        const color = record.status === '审批结束' ? '#b0f323' : '#69acff';
        return h('span', { style: { color } }, record.status);
      },
    },
    {
      title: t('当前进度'),
      dataIndex: 'currentProgress',
      width: 140,
      slots: { customRender: 'currentProgress' },
    },
    {
      title: t('摘要信息'),
      align: 'left',
      dataIndex: 'summaryInfo',
      width: 200,
    },
  ];
  const visibleLookProcess = ref(false);
  const visibleRejectProcess = ref(false);
  const visibleUrgeProcess = ref(false);

  const taskIdProcess = ref('');
  const processIdProcess = ref('');
  const schemaIdProcess = ref('');
  const urgeStatus = ref('');
  const { formConfig } = userTaskTable();
  const [registerTable, { reload }] = useTable({
    title: t('已办任务列表'),
    api: getSchemaTask,
    rowKey: 'id',
    columns: configColumns,
    formConfig: formConfig(),
    beforeFetch: (params) => {
      return { data: params, taskUrl: TaskTypeUrl.FINISHED_TASKS };
    },
    useSearchForm: true,
    showTableSetting: true,
    striped: false,
    showIndexColumn: false,
    pagination: {
      pageSize: 18,
    },
    indexColumnProps: {
      width: 50,
    },
    actionColumn: {
      width: 140,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: undefined,
    },
  });

  function restartProcess() {
    restartProcessVisible.value = true;
  }
  function restartProcessClose() {
    restartProcessVisible.value = false;
    reload();
  }

  function handleUrgeClose() {
    visibleUrgeProcess.value = false;
    reload();
  }

  const handleView = (record) => {
    visibleLookProcess.value = true;
    taskIdProcess.value = record.taskId;
    processIdProcess.value = record.processId;
  };

  const handleRejectClose = () => {
    visibleRejectProcess.value = false;
    reload();
  };

  const handleWithdraw = (record) => {
    visibleRejectProcess.value = true;
    taskIdProcess.value = record.taskId;
    processIdProcess.value = record.processId;
    schemaIdProcess.value = record.schemaId;
  };
  const handleUrge = (record) => {
    visibleUrgeProcess.value = true;
    taskIdProcess.value = record.taskId;
    processIdProcess.value = record.processId;
    urgeStatus.value = record.status;
  };
</script>

<style scoped></style>
