import { reactive, h, ref } from 'vue';
import type { FormProps } from '/@/components/Form';
import { TasksModel } from '/@/api/workflow/model';
import { FormSchema } from '/@/components/Table';
import SelectUser from '/@/components/Form/src/components/SelectUser.vue';
import { useI18n } from '/@/hooks/web/useI18n';
const { t } = useI18n();
export default function () {
  let searchConfig: FormSchema[] = [
    {
      field: 'serialNumber',
      label: t('流水号'),
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: t('请输入流水号'),
      },
    },
    {
      field: 'name',
      label: t('流程名称'),
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: t('请输入流程名称'),
      },
    },
    {
      field: 'taskName',
      label: t('任务名称'),
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: t('请输入任务名称'),
      },
    },
    {
      field: 'originator',
      label: t('发起人'),
      component: 'Input',
      colProps: { span: 8 },
      render: ({ model, field }) => {
        return h(SelectUser, {
          placeholder: t('请选择发起人'),
          value: model[field],
          suffix: 'ant-design:user-outlined',
          onSelectedId: (v) => {
            model[field] = v;
          },
        });
      },
    },
    {
      field: 'searchDate',
      label: t('时间范围'),
      component: 'RangePicker',
      colProps: { span: 8 },
      componentProps: {
        getPopupContainer: () => document.body,
        placeholder: [t('请选择开始日期'), t('请选择结束日期')],
      },
    },
  ];

  const data: {
    rowKey: string;
    selectedRows: TasksModel[];
  } = reactive({
    rowKey: 'taskId',
    selectedRows: [],
  });

  const processId = ref('');
  const taskId = ref('');
  const schemaId = ref('');

  const showBatchApproval = ref(false);
  function selectionChange({ keys, rows }) {
    data.selectedRows = rows;
    if (keys?.length > 1) {
      showBatchApproval.value = true;
    } else {
      showBatchApproval.value = false;
    }
    if (keys?.length > 0) {
      processId.value = rows[0].processId;
      taskId.value = rows[0].taskId;
      schemaId.value = rows[0].schemaId;
    } else {
      processId.value = '';
      taskId.value = '';
      schemaId.value = '';
    }
  }
  function formConfig(type?) {
    if (type == 'MyProcess') {
      searchConfig = searchConfig.filter((o) => {
        return o.field !== 'taskName' && o.field !== 'originator';
      });
    }
    if (type == 'Drafts') {
      searchConfig = searchConfig.filter((o) => {
        return o.field !== 'serialNumber' && o.field !== 'taskName';
      });
    }
    return {
      rowProps: {
        gutter: 16,
      },
      schemas: searchConfig,
      fieldMapToTime: [['searchDate', ['startTime', 'endTime'], 'YYYY-MM-DD', true]],
      showResetButton: false,
    } as FormProps;
  }
  return {
    formConfig,
    data,
    processId,
    taskId,
    schemaId,
    showBatchApproval,
    selectionChange,
  };
}
