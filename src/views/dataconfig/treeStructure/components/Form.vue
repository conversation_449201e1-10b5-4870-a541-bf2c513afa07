<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    showFooter
    destroyOnClose
    :title="getTitle"
    width="70%"
    @ok="handleSubmit"
    @close="handleClose"
  >
    <ModalDrawer v-if="open" ref="modalDrawerRef" :editId="editId" />
  </BasicDrawer>
</template>
<script lang="ts">
  import { defineComponent, ref, computed } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import ModalDrawer from './Modal.vue';
  import { add, edit } from '/@/api/system/generator/treeStructure';
  const { t } = useI18n();
  const isUpdate = ref<boolean>(false);
  const open = ref(false);
  const editId = ref('');
  const modalDrawerRef = ref();
  export default defineComponent({
    name: 'FormDrawer',
    components: {
      BasicDrawer,
      ModalDrawer,
    },
    emits: ['success', 'register'],
    setup(_, { emit }) {
      const { notification } = useMessage();

      const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
        isUpdate.value = !!data?.isUpdate;
        setDrawerProps({ confirmLoading: false });
        setTimeout(() => {
          editId.value = '';
          if (isUpdate.value) {
            editId.value = data.id;
          }
          open.value = true;
        }, 50);
      });

      const getTitle = computed(() => (!isUpdate.value ? t('新增树结构') : t('编辑树结构')));
      function handleClose() {
        closeDrawer();
        open.value = false;
      }
      async function handleSubmit() {
        try {
          let formState = await modalDrawerRef.value.handleSubmit();
          if (formState) {
            setDrawerProps({ confirmLoading: true });
            let { columns, config, ...otherParams } = formState;
            let params = {
              ...otherParams,
              columns: JSON.stringify(columns),
              config: JSON.stringify(config),
            };
            console.log('params: ', params);
            if (!isUpdate.value) {
              //新增
              await add(params);
              notification.success({
                message: t('提示'),
                description: t('新增树结构成功'),
              }); //提示消息
            } else {
              // 编辑
              await edit({ ...params, id: editId.value });
              notification.success({
                message: t('提示'),
                description: t('修改树结构成功'),
              }); //提示消息
            }
            closeDrawer();
            emit('success');
          } else {
            setDrawerProps({ confirmLoading: false });
          }
        } catch (error) {
          setDrawerProps({ confirmLoading: false });
        }
      }
      return {
        registerDrawer,
        getTitle,
        handleSubmit,
        handleClose,
        t,
        open,
        editId,
        modalDrawerRef,
      };
    },
  });
</script>
