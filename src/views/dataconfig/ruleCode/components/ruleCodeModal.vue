<template>
  <BasicModal
    v-bind="$attrs"
    destroyOnClose
    @register="registerModal"
    showFooter
    :title="getTitle"
    width="55%"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" :style="{ 'margin-right': '10px' }">
      <template #code="{ model }">
        <CodeEditor v-model:value="model.codeData" language="json" />
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form/index';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { CodeEditor } from '/@/components/CodeEditor';
  import { addRuleCode, updateRuleCode, getRuleCodeInfo } from '/@/api/system/ruleCode';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const formSchema: FormSchema[] = [
    {
      title: '基本信息',
      field: 'codeNumber',
      label: '规则代码编号',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入规则代码编号',
      },
      colProps: { span: 12 },
    },

    {
      field: 'methodName',
      label: '规则代码方法名',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入规则代码方法名',
      },
      colProps: { span: 12 },
    },
    {
      field: 'remark',
      label: t('备注'),
      component: 'InputTextArea',
      componentProps: {
        placeholder: t('请输入备注'),
      },
      colProps: { span: 24 },
    },
    {
      title: '代码编辑区',
      field: 'codeData',
      label: ' ',
      component: 'Input',
      slot: 'code',
      colProps: { span: 24 },
    },
  ];

  const codeData = 'package com.zilue.module.liteflow.example.components; //生成路径 可修改';

  const emit = defineEmits(['success', 'register']);
  const isUpdate = ref(true);
  const { notification } = useMessage();
  const rowId = ref('');
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: formSchema,
    showActionButtonGroup: false,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false, fixedHeight: true, defaultFullscreen: true });
    isUpdate.value = !!data?.isUpdate;

    if (unref(isUpdate)) {
      rowId.value = data.id;
      const info = await getRuleCodeInfo(rowId.value);
      setTimeout(() => {
        //使用setTimeout解决编辑器格式不对齐问题
        setFieldsValue(info);
      }, 100);
    } else {
      setFieldsValue({
        codeData,
      });
    }
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '新增规则代码' : '编辑规则代码'));

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });
      if (!unref(isUpdate)) {
        await addRuleCode(values);
        notification.success({
          message: t('提示'),
          description: t('新增成功'),
        });
      } else {
        values.id = rowId.value;
        await updateRuleCode(values);
        notification.success({
          message: t('提示'),
          description: t('编辑成功'),
        });
      }
      closeModal();
      emit('success');
    } catch (error) {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
