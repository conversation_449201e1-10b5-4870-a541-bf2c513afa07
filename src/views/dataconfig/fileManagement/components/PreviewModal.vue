<template>
  <BasicModalExcel v-bind="$attrs" @register="registerModal" wrapClassName="modal-box">
    <div class="preview-title">{{ fileName }}</div>
    <iframe :src="previewFile" class="iframe-box"></iframe>
    <template #footer>
      <a-button @click="closeModal">关闭</a-button>
    </template>
  </BasicModalExcel>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModalExcel, useModalInner } from '/@/components/Modal';
  import { getAppEnvConfig } from '/@/utils/env';
  import { Base64 } from 'js-base64';
  import previewPng from '/@/assets/file/preview.png';

  const previewFile = ref('');
  const fileName = ref('');

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({
      confirmLoading: false,
      fixedHeight: true,
      width: 700,
      title: '文件预览',
      imgSrc: previewPng,
    });
    fileName.value = data.fileName;
    previewFile.value =
      getAppEnvConfig().VITE_GLOB_UPLOAD_PREVIEW +
      encodeURIComponent(
        Base64.encode(
          data.fileUrl.includes('http://') || data.fileUrl.includes('https://')
            ? data.fileUrl
            : getAppEnvConfig().VITE_GLOB_API_URL + data.fileUrl,
        ),
      );
  });
</script>
<style lang="less" scoped>
  .preview-title {
    text-align: center;
    font-weight: bold;
    margin-bottom: 10px;
    line-height: 20px;
  }

  .iframe-box {
    width: 100%;
    height: calc(100% - 30px);
    border: 1px solid #ccc;
    padding: 10px;
  }
</style>
