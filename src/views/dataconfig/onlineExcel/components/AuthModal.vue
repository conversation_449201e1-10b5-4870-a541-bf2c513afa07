<template>
  <BasicModalExcel
    v-bind="$attrs"
    @register="registerModal"
    @ok="handleSubmit"
    wrapClassName="modal-box"
  >
    <BasicTable @register="registerTable">
      <template #toolbar>
        <div v-for="(item, index) in allComponentList" :key="index">
          <component :is="item.component" :memberList="authList" @change="changeList">
            <a-button :type="item.type" v-auth="`excel:${item.auth}`" class="!rounded">
              {{ item.name }}
            </a-button>
          </component>
        </div>
      </template>
      <template #action="{ index }">
        <TableAction
          :actions="[
            {
              img: deletePng,
              color: 'error',
              onClick: handleDelete.bind(null, index),
            },
          ]"
        />
      </template>
    </BasicTable>
  </BasicModalExcel>
</template>
<script lang="tsx" setup>
  import { ref, computed } from 'vue';
  import { BasicModalExcel, useModalInner } from '/@/components/Modal';
  import { BasicTable, useTable, TableAction, BasicColumn } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getExcelAuth, updateExcelAuth } from '/@/api/system/excel';
  import { ExcelAuthParamsModel } from '/@/api/system/excel/model/index';
  import { useI18n } from '/@/hooks/web/useI18n';
  import Posts from '/@bpmn/components/member/Posts.vue';
  import Roles from '/@bpmn/components/member/Roles.vue';
  import Users from '/@bpmn/components/member/Users.vue';
  import { MemberType } from '/@/enums/workflowEnum';
  import deletePng from '/@/assets/excel/delete.png';
  import authModalPng from '/@/assets/excel/authModal.png';

  interface AuthList {
    id: string;
    name: string;
    memberType: number;
  }

  const columns: BasicColumn[] = [
    {
      title: '类型',
      dataIndex: 'memberType',
      customRender: ({ record }) => {
        return getMemberType(record.memberType);
      },
    },

    {
      title: '名称',
      dataIndex: 'name',
    },
  ];

  const allComponentList = computed(() => {
    return [
      { name: t('添加岗位'), component: Posts, auth: 'post', type: 'primary' },
      { name: t('添加角色'), component: Roles, auth: 'role', type: 'default' },
      { name: t('添加人员'), component: Users, auth: 'user', type: 'default' },
    ];
  });

  const getMemberType = (val: MemberType) => {
    if (val === MemberType.POST) return t('岗位');
    if (val === MemberType.ROLE) return t('角色');
    if (val === MemberType.USER) return t('人员');
    return val;
  };
  const { t } = useI18n();
  const emit = defineEmits(['success', 'register']);

  const { notification } = useMessage();
  const rowId = ref('');
  const authList = ref<AuthList[]>([]);

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({
      confirmLoading: false,
      destroyOnClose: true,
      fixedHeight: true,
      canFullscreen: false,
      width: 600,
      title: '权限设置',
      imgSrc: authModalPng,
    });
    rowId.value = data.id;
    const list = await getExcelAuth(data.id);
    authList.value = list.map((item) => {
      return {
        id: item.objectId,
        name: item.objectName,
        memberType: item.authType,
      };
    });
    setTableData(authList.value);
  });

  const [registerTable, { setTableData }] = useTable({
    columns,
    showIndexColumn: false,
    striped: false,
    pagination: false,
    actionColumn: {
      width: 80,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  });

  const changeList = (list) => {
    authList.value = list;
    setTableData(authList.value);
  };

  const handleDelete = (index) => {
    authList.value.splice(index, 1);
    setTableData(authList.value);
  };
  async function handleSubmit() {
    try {
      const excelAuthDtoList = authList.value.map((item) => {
        return {
          objectName: item.name,
          authType: item.memberType,
          objectId: item.id,
        };
      });
      const params: ExcelAuthParamsModel = {
        excelAuthDtoList: excelAuthDtoList,
        excelId: rowId.value,
      };
      await updateExcelAuth(params);
      emit('success');
      closeModal();
      notification.success({
        message: t('提示'),
        description: '权限设置成功',
      });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
