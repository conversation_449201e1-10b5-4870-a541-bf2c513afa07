<template>
  <div class="schedule-box">
    <FullCalendar :options="scheduleData" />
    <ScheduleModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import FullCalendar from '@fullcalendar/vue3';
  import dayGridPlugin from '@fullcalendar/daygrid';
  import timeGridPlugin from '@fullcalendar/timegrid';
  import interactionPlugin from '@fullcalendar/interaction';
  import { CalendarOptions } from '@fullcalendar/core';
  import { getScheduleList } from '/@/api/system/schedule';
  import ScheduleModal from './components/ScheduleModal.vue';
  import { useModal } from '/@/components/Modal';

  const handleDateClick = (day) => {
    openModal(true, { date: day.dateStr });
  };

  const handleEventClick = ({ event }) => {
    openModal(true, { eventId: event.id });
  };
  const [registerModal, { openModal }] = useModal();

  const scheduleData = ref<CalendarOptions>({
    plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],
    initialView: 'dayGridMonth',
    dateClick: handleDateClick,
    eventClick: handleEventClick,
    locale: 'zh-cn',
    themeSystem: '#000',
    headerToolbar: {
      left: '',
      center: 'prev,title,next',
      right: '',
    },
    events: [],
  });

  onMounted(() => {
    handleSuccess();
  });

  const handleSuccess = async () => {
    scheduleData.value.events = await getScheduleList();
  };
</script>
<style lang="less" scoped>
  :deep(.fc-toolbar-chunk) > div {
    display: flex;
  }

  :deep(.fc-button),
  :deep(.fc-button:active),
  :deep(.fc-button:hover) {
    background-color: #fff !important;
    border: none !important;
    color: #000 !important;
    box-shadow: none !important;
  }

  :deep(.fc-direction-ltr) {
    background: #fff;
  }

  .schedule-box {
    margin: 10px;
    padding: 10px;
    background-color: #fff;
  }
</style>
