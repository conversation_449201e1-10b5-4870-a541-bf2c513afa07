<template>
  <PageWrapper dense contentFullHeight fixedHeight contentClass="flex">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate" v-auth="'devmanagement:add'">{{
          t('新增')
        }}</a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'jiekoushouquan|svg',
              auth: 'devmanagement:apiAuth',
              tooltip: '接口授权',
              onClick: handleAuth.bind(null, record),
            },
            {
              icon: 'clarity:note-edit-line',
              auth: 'devmanagement:edit',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ant-design:delete-outlined',
              auth: 'devmanagement:delete',
              color: 'error',
              popConfirm: {
                title: t('是否确认删除'),
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
        />
      </template>
    </BasicTable>
    <ManagementModal @register="registerModal" @success="handleSuccess" />
    <ApiSelect
      v-if="apiSelectDialog"
      isMultiple
      v-model:apiSelectDialog="apiSelectDialog"
      v-model:selectedApiId="apiIds"
      @success="handleApiSuccess"
    />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicTable, useTable, TableAction, FormSchema, BasicColumn } from '/@/components/Table';
  import {
    deleteDeveloper,
    getDeveloperPageList,
    getDeveloperAuth,
    updateDeveloperAuth,
  } from '/@/api/system/developer';
  import { PageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useModal } from '/@/components/Modal';
  import ManagementModal from './components/ManagementModal.vue';
  import ApiSelect from '/@/components/ApiConfig/src/components/ApiConfigSelect.vue';
  import { usePermission } from '/@/hooks/web/usePermission';

  const { t } = useI18n();
  const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: t('关键字'),
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请输入名称或编号',
      },
    },
  ];

  const columns: BasicColumn[] = [
    {
      title: '编号',
      dataIndex: 'code',
      align: 'left',
      resizable: true,
    },

    {
      title: '名称',
      dataIndex: 'name',
      align: 'left',
      resizable: true,
    },
    {
      title: '使用期限',
      dataIndex: 'expireDate',
      align: 'left',
      resizable: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      align: 'left',
      resizable: true,
    },
    {
      title: '创建人',
      dataIndex: 'createUserName',
      align: 'left',
      resizable: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createDate',
      align: 'left',
      resizable: true,
    },
  ];

  const apiSelectDialog = ref<boolean>(false);
  const apiIds = ref<string[]>([]);
  const selectedId = ref('');

  const { notification } = useMessage();
  const { hasPermission } = usePermission();

  const [registerModal, { openModal }] = useModal();
  const [registerTable, { reload }] = useTable({
    title: '开发者管理',
    api: getDeveloperPageList,
    rowKey: 'id',
    columns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      showResetButton: false,
    },
    useSearchForm: true,
    striped: false,
    showTableSetting: true,
    actionColumn: {
      width: 120,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
    tableSetting: {
      size: false,
    },
    customRow: (record) => {
      return {
        ondblclick: () => {
          if (hasPermission('devmanagement:edit')) {
            handleEdit(record);
          }
        },
      };
    },
  });

  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    openModal(true, {
      id: record.id,
      isUpdate: true,
    });
  }

  function handleDelete(record: Recordable) {
    deleteDeveloper([record.id]).then(() => {
      reload();
      notification.success({
        message: t('提示'),
        description: t('删除成功'),
      }); //提示消息
    });
  }

  function handleSuccess() {
    reload();
  }

  async function handleAuth(record) {
    selectedId.value = record.id;
    apiIds.value = (await getDeveloperAuth(selectedId.value)) || [];
    apiSelectDialog.value = true;
  }

  async function handleApiSuccess() {
    const params = {
      id: selectedId.value,
      interfaceIds: apiIds.value,
    };
    await updateDeveloperAuth(params);
    apiSelectDialog.value = false;
    notification.success({
      message: t('成功'),
      description: '接口授权成功',
    });
  }
</script>
