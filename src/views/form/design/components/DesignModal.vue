// 自定义表单
<template>
  <BasicModal @register="registerModal" v-bind="$attrs">
    <GroupCard :growCardList="items" />
    <DataFirstModal
      v-if="state.isShowDataFirst"
      @register="registerDataFirst"
      @success="handleSuccess"
      @close="handleClose('isShowDataFirst')"
    />
    <CodeFirstModal
      v-if="state.isShowCodeFirst"
      @register="registerCodeFirst"
      @success="handleSuccess"
      @close="handleClose('isShowCodeFirst')"
    />
    <SimpleTemplateModal
      v-if="state.isShowSimpleTemplate"
      @register="registerSimpleTemplate"
      @success="handleSuccess"
      @close="handleClose('isShowSimpleTemplate')"
    />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { reactive } from 'vue';
  import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
  import GroupCard from '/@/views/generator/dev/components/GroupCard.vue';
  import DataFirstModal from './components/DataFirstModal.vue';
  import CodeFirstModal from './components/CodeFirstModal.vue';
  import SimpleTemplateModal from './components/SimpleTemplateModal.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useRouter } from 'vue-router';
  import interfaceFirstPng from '/@/assets/images/design/interfaceFirst.png';
  import dataFirst from '/@/assets/images/design/dataFirst.png';
  import simpleTemplate from '/@/assets/images/design/simpleTemplate.png';
  import quickDevelop from '/@/assets/images/design/quickDevelop.png';
  const router = useRouter();
  const { t } = useI18n();
  const [registerDataFirst, { openModal: openDataFirstModal }] = useModal();
  const [registerCodeFirst, { openModal: openCodeFirstModal }] = useModal();
  const [registerSimpleTemplate, { openModal: openSimpleTemplateModal }] = useModal();

  const emits = defineEmits(['success', 'register']);

  const items = [
    {
      name: t('界面优先'),
      content: t('以界面为基础设计并生成单表或多表的基础功能'),
      btnName: t('设计功能'),
      image: interfaceFirstPng,
      func: () => {
        openCodeFirstModal(true, { undoAble: true });
      },
    },
    {
      name: t('数据优先'),
      content: t('以数据为基础设置生成单表或多表的基础功能'),
      btnName: t('设计功能'),
      image: dataFirst,
      func: () => {
        openDataFirstModal(true, { undoAble: true });
      },
    },
    {
      name: t('简易模板'),
      content: t('通过简单操作生成不需要考虑数据库的基础功能'),
      btnName: t('设计功能'),
      image: simpleTemplate,
      func: () => {
        openSimpleTemplateModal(true, { undoAble: true });
      },
    },
    {
      name: t('快速生成代码'),
      content: t('根据数据表结构自动快速生成功能代码'),
      btnName: t('设计功能'),
      image: quickDevelop,
      func: () => {
        closeModal();
        router.push({ path: '/dataconfig/database' });
      },
    },
  ];

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({
      confirmLoading: false,
      canFullscreen: false,
      defaultFullscreen: true,
      draggable: false,
      title: data.title,
      showOkBtn: false,
      showCancelBtn: false,
    });
  });
  const state = reactive({
    isShowDataFirst: true,
    isShowCodeFirst: true,
    isShowSimpleTemplate: true,
  });
  const handleClose = (modal) => {
    state[modal] = !state[modal];
    setTimeout(() => {
      state[modal] = !state[modal];
    }, 100);
  };

  const handleSuccess = () => {
    emits('success');
    closeModal();
  };
</script>
