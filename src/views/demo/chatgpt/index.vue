<template>
  <PageWrapper :title="t('ChatGpt示例')">
    <div>
      <Card :title="t('回答区')">
        <MarkdownViewer :value="resultRef" />
      </Card>
    </div>
    <div class="mt-2">
      <Card :title="t('输入区')">
        {{ t('请输入：') }}
        <Input v-model:value="valueRef" />
        <Button @click="send">{{ t('提交') }}</Button>
      </Card>
    </div>
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { MarkdownViewer } from '/@/components/Markdown';
  import { PageWrapper } from '/@/components/Page';
  import { Card, Input, Button } from 'ant-design-vue';
  import { ref } from 'vue';
  import { sendChatGpt } from '/@/api/chatgpt';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const valueRef = ref(``);
  const resultRef = ref(``);

  const send = async () => {
    resultRef.value = await sendChatGpt(valueRef.value);
  };
</script>
