<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    showFooter
    destroyOnClose
    :title="getTitle"
    width="70%"
    @ok="handleSubmit"
  >
    <ModalDrawer v-if="open" ref="modalDrawerRef" :editId="editId" />
  </BasicDrawer>
</template>
<script lang="ts">
  import { defineComponent, ref, computed } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import ModalDrawer from './Modal.vue';
  import { add, edit } from '/@/api/system/generator/messageTemplate';
  const { t } = useI18n();
  const isUpdate = ref<boolean>(false);
  const open = ref(false);
  const editId = ref('');
  const modalDrawerRef = ref();
  export default defineComponent({
    name: 'FormDrawer',
    components: {
      <PERSON>Drawer,
      ModalDrawer,
    },
    emits: ['success', 'register'],
    setup(_, { emit }) {
      const { notification } = useMessage();

      const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
        setDrawerProps({ confirmLoading: false });
        isUpdate.value = !!data?.isUpdate;
        editId.value = '';
        if (isUpdate.value) {
          editId.value = data.id;
        }
        open.value = true;
      });

      const getTitle = computed(() => (!isUpdate.value ? t('新增消息模板') : t('编辑消息模板')));
      async function handleSubmit() {
        try {
          let formState = await modalDrawerRef.value.handleSubmit();
          if (formState) {
            setDrawerProps({ confirmLoading: true });
            if (!isUpdate.value) {
              //新增
              await add(formState);
              notification.success({
                message: t('提示'),
                description: t('新增菜单成功'),
              }); //提示消息
            } else {
              // 编辑
              await edit(formState);
              notification.success({
                message: t('提示'),
                description: t('修改菜单成功'),
              }); //提示消息
            }
            closeDrawer();
            emit('success');
          } else {
            setDrawerProps({ confirmLoading: false });
          }
        } catch (error) {
          setDrawerProps({ confirmLoading: false });
        }
      }
      return {
        registerDrawer,
        getTitle,
        handleSubmit,
        t,
        open,
        editId,
        modalDrawerRef,
      };
    },
  });
</script>
