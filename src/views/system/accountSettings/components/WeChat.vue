<template>
  <div class="flex flex-col items-center justify-center py-8 bg-white">
    <div class="form-box">
      <a-form
        :model="formState"
        name="basic"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
        ref="FormRef"
      >
        <a-form-item
          label="AppId"
          name="appId"
          :rules="[{ required: true, message: '请填写AppId!' }]"
        >
          <a-input v-model:value="formState.appId" placeholder="请填写" />
        </a-form-item>
        <!-- <a-form-item
          label="AppKey"
          name="appKey"
          :rules="[{ required: true, message: '请填写AppKey!' }]"
        >
          <a-input v-model:value="formState.appKey" placeholder="请填写" />
        </a-form-item> -->
        <a-form-item
          label="AppSecret"
          name="appSecret"
          :rules="[{ required: true, message: '请填写AppSecret!' }]"
        >
          <a-input v-model:value="formState.appSecret" placeholder="请填写" />
        </a-form-item>
        <a-form-item :wrapper-col="{ offset: 4, span: 20 }">
          <a-button class="!ml-4" type="primary" @click="handleSubmit">
            {{ t('确认') }}
          </a-button>
        </a-form-item>
      </a-form>
    </div>
    <div class="flex justify-center"></div>
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, reactive, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { set, getInfo } from '/@/api/system/generator/accountSettings';
  import { MessageType } from '/@/enums/messageTemplate';
  import { useMessage } from '/@/hooks/web/useMessage';
  const { notification } = useMessage();
  const FormRef = ref();
  const { t } = useI18n();

  interface FormState {
    appId: string;
    // appKey: string;
    appSecret: string;
  }

  const formState = reactive<FormState>({
    appId: '',
    // appKey: '',
    appSecret: '',
  });
  onMounted(async () => {
    let res = await getInfo(MessageType.WE_CHAT);
    if (res && res.configJson) {
      let config = JSON.parse(res.configJson);
      if (config.appId) formState.appId = config.appId;
      // if (config.appKey) formState.appKey = config.appKey;
      if (config.appSecret) formState.appSecret = config.appSecret;
    }
  });
  async function handleSubmit() {
    try {
      await FormRef.value.validate();
      await set(MessageType.WE_CHAT, formState);
      notification.success({
        message: t('提示'),
        description: t('配置成功'),
      }); //提示消息
    } catch (error) {}
  }
</script>
<style lang="less" scoped>
  .form-box {
    width: 100%;
    padding: 0 20px;
  }
</style>
