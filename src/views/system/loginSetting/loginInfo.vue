<template>
  <div class="p-4 bg-white h-full">
    <div class="pb-4 text-xl">{{ t('框架登录配置') }}</div>
    <div class="step1-form">
      <a-form
        :model="formState"
        name="basic"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 21 }"
        autocomplete="off"
        @finish="onFinish"
        @finish-failed="onFinishFailed"
      >
        <a-form-item :label="t('多端登录')" name="mulLogin">
          <div class="flex">
            <div
              v-for="item in loginType"
              :key="item.value"
              class="gouBox"
              :class="formState.mulLogin?.includes(item.value) ? 'active' : ''"
              @click="handleClick(item.value)"
              >{{ item.name }}
              <div class="triangle" v-if="formState.mulLogin?.includes(item.value)">
                <Icon color="#fff" icon="uil:check" />
              </div>
            </div>
          </div>
          <a-tooltip placement="bottomLeft">
            <template #title>
              <p style="max-width: 336px"
                >1.多端登录是指是否允许当前系统的用户登录Web端及移动端。</p
              ></template
            >
            <QuestionCircleFilled
              style="
                font-size: 16px;
                color: rgb(204 204 204);
                position: absolute;
                left: -90px;
                top: 8px;
              "
            />
          </a-tooltip>
        </a-form-item>

        <a-form-item :label="t('同端互斥')" name="mutualExclusion">
          <a-switch
            v-model:checked="formState.mutualExclusion"
            :checkedValue="1"
            :unCheckedValue="0"
            :disabled="proDisabled"
            @change="handleChange()"
          />
          <a-tooltip placement="bottomLeft">
            <template #title>
              <p style="max-width: 336px"
                >1.同端互斥是指账号进行重复登录时，之前登录的人员将会跳回到登录页，正在登录的人员则可以正常使用。</p
              ></template
            >
            <QuestionCircleFilled
              style="
                font-size: 16px;
                color: rgb(204 204 204);
                position: absolute;
                left: -90px;
                top: 8px;
              "
            />
          </a-tooltip>
        </a-form-item>

        <a-form-item :label="t('七天免登')" name="withoutLogin">
          <a-switch
            v-model:checked="formState.withoutLogin"
            :checkedValue="1"
            :unCheckedValue="0"
          />
        </a-form-item>
        <a-form-item :label="t('密码策略')" name="passwordStrategy">
          <div class="flex">
            <div
              class="gouBox"
              :class="formState.passwordStrategy ? 'active' : ''"
              @click="handlePassClick()"
              >{{ t('密码错误超过最大次数锁定') }}
              <div class="triangle" v-if="formState.passwordStrategy"
                ><Icon color="#fff" icon="uil:check"
              /></div>
            </div>
          </div>
          <a-tooltip placement="bottomLeft">
            <template #title>
              <div style="max-width: 336px">
                <p>1.密码策略是指在登录过程中出现密码错误情况时的系统处理方式。</p>
                <p>2.默认“密码错误超过最大次数锁定”的策略状态为启用。</p>
                <p
                  >3.当用户的账号被锁定后，需要联系管理员在用户管理中心进行解锁才可重新登录，否则提示用户“当前账号已被锁定，请联系管理员。”。</p
                >
              </div>
            </template>
            <QuestionCircleFilled
              style="
                font-size: 16px;
                color: rgb(204 204 204);
                position: absolute;
                left: -90px;
                top: 8px;
              "
            />
          </a-tooltip>
        </a-form-item>
        <a-form-item :label="t('最大次数')" name="strategyMaxNumber">
          <a-input-number
            v-model:value="formState.strategyMaxNumber"
            :placeholder="t('请输入最大次数')"
            style="width: 50%"
          />
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 6, span: 24 }">
          <a-button type="primary" html-type="submit" class="ml-4">{{ t('提交') }}</a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { computed, onMounted, ref } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { QuestionCircleFilled } from '@ant-design/icons-vue';
  import { Modal } from 'ant-design-vue';
  import { h } from 'vue';
  import { getLoginInfo, setLoginConfig } from '/@/api/system/login';
  import { useMessage } from '/@/hooks/web/useMessage';
  const { t } = useI18n();
  const { notification } = useMessage();
  const loginType = [
    { name: 'Web登录', value: '0' },
    { name: '移动端登录', value: '1' },
  ];
  interface FormState {
    mulLogin?: string[];
    mutualExclusion?: number;
    withoutLogin?: number;
    strategyMaxNumber?: number | null;
    passwordStrategy?: number;
  }

  let formState = ref<FormState>({});
  const id = ref();

  const proDisabled = computed(() => {
    return import.meta.env.VITE_GLOB_PRODUCTION === 'true';
  });
  onMounted(() => {
    getLoginInfo().then((res) => {
      console.log(res);
      formState.value = {
        mulLogin: res.mulLogin.split(','),
        mutualExclusion: res.mutualExclusion,
        withoutLogin: res.withoutLogin,
        strategyMaxNumber: res.strategyMaxNumber,
        passwordStrategy: res.passwordStrategy,
      };
      id.value = res.id;
    });
  });
  const onFinish = (values: any) => {
    let params = values;
    params.id = id.value;
    if (params.mulLogin) params.mulLogin = params.mulLogin.join(',');
    setLoginConfig(params).then((res) => {
      if (res) {
        notification.success({
          message: t('提示'),
          description: t('修改成功'),
        });
      } else {
        notification.success({
          message: t('提示'),
          description: t('修改失败'),
        });
      }
    });
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  function handlePassClick() {
    if (formState.value.passwordStrategy === 0) {
      formState.value.passwordStrategy = 1;
      formState.value.strategyMaxNumber = null;
    } else {
      formState.value.passwordStrategy = 0;
    }
  }

  function handleClick(val) {
    if (proDisabled.value) return;
    if (formState.value.mulLogin?.includes(val)) {
      formState.value.mulLogin = formState.value.mulLogin.filter((o) => {
        return o != val;
      });
    } else {
      formState.value.mulLogin?.push(val);
    }
  }

  const handleChange = () => {
    Modal.info({
      title: '提示',
      content: h('div', {}, [h('p', '修改同端互斥状态需重启后端！')]),
    });
  };
</script>
<style lang="less">
  .gouBox {
    border-width: 1px;
    border-style: solid;
    border-color: rgb(228 228 228);
    height: 35px;
    line-height: 35px;
    color: rgb(102 102 102);
    padding: 0 30px 0 20px;
    margin-right: 10px;
    position: relative;

    &:hover {
      background-color: rgb(249 249 249);
    }

    &.active {
      color: rgb(64 158 255);
      background-color: rgb(240 247 255 / 99.6%);
      border-color: rgb(160 207 255);

      &:hover {
        background-color: rgb(240 247 255 / 99.6%);
      }
    }

    .triangle {
      width: 0;
      height: 0;
      border-bottom: 25px solid rgb(64 158 255);
      border-left: 25px solid transparent;
      position: absolute;
      right: 0;
      bottom: 0;

      span {
        position: absolute;
        right: 0;
        bottom: -25px;
      }
    }
  }
</style>
