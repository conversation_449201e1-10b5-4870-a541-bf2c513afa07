<template>
  <PageWrapper dense fixedHeight contentFullHeight>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" v-auth="'index:add'" @click="handleCreate">
          {{ t('新增') }}
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="['authObjectList'].includes(column.dataIndex)">
          <a-tag
            class="cursor-pointer"
            color="blue"
            @click="viewObject(record.id, record.authType)"
            >{{ t('查看授权对象') }}</a-tag
          >
        </template>
        <template v-if="column.dataIndex == 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:note-edit-line',
                auth: 'index:edit',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                auth: 'index:delete',
                color: 'error',
                popConfirm: {
                  title: t('是否确认删除'),
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <AuthDrawer @register="registerDrawer" @success="handleSuccess" />
    <AuthModal @register="registerModal" />
  </PageWrapper>
</template>
<script lang="ts">
  import { defineComponent, h } from 'vue';

  import { BasicTable, useTable, TableAction, BasicColumn, FormSchema } from '/@/components/Table';
  import AuthModal from './components/AuthModal.vue';
  import { useDrawer } from '/@/components/Drawer';
  import AuthDrawer from './components/AuthDrawer.vue';
  import { PageWrapper } from '/@/components/Page';
  import { Tag } from 'ant-design-vue';
  import { useMessage } from '/@/hooks/web/useMessage';

  import { deleteAuth, getAuth, getAuthObject, getAuthPageList } from '/@/api/system/authorize';
  import { usePermission } from '/@/hooks/web/usePermission';

  import { useModal } from '/@/components/Modal';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  export const columns: BasicColumn[] = [
    {
      title: t('编码'),
      dataIndex: 'code',
      sorter: true,
      align: 'left',
      resizable: true,
    },
    {
      title: t('名称'),
      dataIndex: 'name',
      sorter: true,
      align: 'left',
      resizable: true,
    },
    {
      title: t('对象类型'),
      dataIndex: 'authType',
      align: 'left',
      resizable: true,
      sorter: true,
      customRender: ({ record }) => {
        const authType = record.authType;
        const color = authType == 1 ? 'yellow' : 'red';
        const text = authType == 1 ? t('用户') : t('角色');
        return h(Tag, { color: color }, () => text);
      },
    },
    {
      title: t('授权对象'),
      dataIndex: 'authObjectList',
      ellipsis: false,
      align: 'left',
      resizable: true,
    },
    {
      title: t('创建人'),
      dataIndex: 'createUserName',
      align: 'left',
      resizable: true,
    },
    {
      title: t('创建时间'),
      dataIndex: 'createDate',
      sorter: true,
      align: 'left',
      resizable: true,
    },
    {
      title: t('最后修改人'),
      dataIndex: 'modifyUserName',
      align: 'left',
      resizable: true,
    },
    {
      title: t('最后修改时间'),
      dataIndex: 'modifyDate',
      sorter: true,
      align: 'left',
      resizable: true,
    },
  ];

  export const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: t('关键字'),
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: t('请输入名称或编码'),
      },
    },
  ];

  export default defineComponent({
    name: 'AuthManagement',
    components: { BasicTable, AuthDrawer, TableAction, AuthModal, PageWrapper },
    setup() {
      const { notification } = useMessage();
      const { hasPermission } = usePermission();

      const [registerDrawer, { openDrawer }] = useDrawer();
      const [registerModal, { openModal, setModalProps }] = useModal();
      const [registerTable, { reload }] = useTable({
        title: t('数据权限列表'),
        api: getAuthPageList,
        columns,
        formConfig: {
          rowProps: {
            gutter: 16,
          },
          schemas: searchFormSchema,
          showResetButton: false,
        },
        useSearchForm: true,
        showTableSetting: true,
        striped: false,
        actionColumn: {
          width: 80,
          title: t('操作'),
          dataIndex: 'action',
          slots: { customRender: 'action' },
        },
        tableSetting: {
          size: false,
        },
        customRow: (record) => {
          return {
            ondblclick: () => {
              if (hasPermission('index:edit')) {
                handleEdit(record);
              }
            },
          };
        },
      });

      function handleCreate() {
        openDrawer(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        getAuth(record.id).then((res) => {
          openDrawer(true, {
            record: res,
            isUpdate: true,
          });
        });
      }

      function handleDelete(record: Recordable) {
        deleteAuth([record.id]).then((_) => {
          reload();
          notification.success({
            message: t('提示'),
            description: t('删除成功'),
          }); //提示消息
        });
      }

      function handleSuccess() {
        reload();
      }
      function viewObject(id, type) {
        getAuthObject(id).then((res) => {
          openModal(true, res);
          setModalProps({ title: type == 1 ? t('查看用户授权对象') : t('查看角色授权对象') });
        });
      }
      return {
        registerTable,
        registerDrawer,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
        viewObject,
        registerModal,
        t,
      };
    },
  });
</script>
