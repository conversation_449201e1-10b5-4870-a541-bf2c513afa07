<template>
  <ResizePageWrapper>
    <template #resizeLeft>
      <DeptTree @select="handleSelect" />
    </template>
    <template #resizeRight>
      <BasicTable @register="registerTable">
        <template #toolbar>
          <a-button type="primary" v-auth="'user:add'" @click="handleCreate">{{
            t('新增用户')
          }}</a-button>
        </template>
        <template #action="{ record, index }">
          <TableAction
            :actions="[
              {
                icon: record.enabledMark === 1 ? 'zhanghusuoding|svg' : 'jiesuo|svg',
                auth: 'user:unlock',
                tooltip: record.enabledMark === 1 ? '锁定' : '解锁',
                onClick: handleLock.bind(null, record),
              },
              {
                icon: 'zhongzhimima|svg',
                auth: 'user:resetPassword',
                tooltip: '重置密码',
                onClick: handleReset.bind(null, record),
              },
              {
                icon: 'clarity:note-edit-line',
                auth: 'user:edit',
                onClick: handleEdit.bind(null, record),
              },
              // {
              //   icon: 'ant-design:delete-outlined',
              //   auth: 'user:delete',
              //   color: 'error',
              //   popConfirm: {
              //     title: t('是否确认删除'),
              //     visible: popVisible(index),
              //     onVisibleChange: handleVisibleChange.bind(null, index),
              //     confirm: handleDelete.bind(null, record),
              //     cancel: handleCancel,
              //   },
              // },
            ]"
          />
        </template>
      </BasicTable>
    </template>
    <AccountModal @register="registerModal" @success="handleSuccess" />
  </ResizePageWrapper>
</template>
<script lang="ts">
  import { defineComponent, h, ref } from 'vue';
  import { BasicTable, useTable, TableAction, FormSchema, BasicColumn } from '/@/components/Table';
  import { getUserPageList, deleteUser, resetUserPassword, userEnabled } from '/@/api/system/user';
  import { ResizePageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermission } from '/@/hooks/web/usePermission';

  import { useModal } from '/@/components/Modal';
  import AccountModal from './components/UserModal.vue';
  import DeptTree from './components/DeptTree.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { Tag } from 'ant-design-vue';
  import { getRoleAllList } from '/@/api/system/role';
  const { t } = useI18n();
  export const searchFormSchema: FormSchema[] = [
    {
      field: 'code',
      label: t('ERP编码'),
      component: 'Input',
      colProps: { lg: 8, md: 24 },
    },
    {
      field: 'name',
      label: t('姓名'),
      component: 'Input',
      colProps: { lg: 8, md: 24 },
    },
    {
      field: 'mobile',
      label: t('联系方式'),
      component: 'Input',
      colProps: { lg: 8, md: 24 },
    },
    {
      field: 'roleId',
      label: t('角色'),
      component: 'ApiTreeSelect',
      componentProps: () => {
        return {
          api: getRoleAllList,
          fieldNames: {
            key: 'id',
            label: 'name',
            value: 'id',
          },
          getPopupContainer: () => document.body,
        };
      },
      colProps: { lg: 8, md: 24 },
    },
    {
      field: 'enabledMark',
      label: t('账号状态'),
      component: 'Select',
      componentProps: {
        options: [
          { label: '锁定', value: '0' },
          { label: '正常', value: '1' },
        ],
      },
      colProps: { lg: 8, md: 24 },
    },
  ];

  export const columns: BasicColumn[] = [
    {
      title: t('ID'),
      dataIndex: 'id',
      width: 120,
      sorter: true,
      align: 'left',
      resizable: true,
    },
    {
      title: t('用户姓名'),
      dataIndex: 'name',
      width: 120,
      sorter: true,
      align: 'left',
      resizable: true,
    },
    {
      title: t('用户账号'),
      dataIndex: 'mobile',
      width: 120,
      sorter: true,
      align: 'left',
      resizable: true,
    },
    {
      title: t('ERP编码'),
      dataIndex: 'code',
      width: 180,
      sorter: true,
      align: 'left',
      resizable: true,
    },
    {
      title: t('联系方式'),
      dataIndex: 'mobile',
      width: 120,
      align: 'left',
      resizable: true,
      sorter: true,
    },
    {
      title: t('所属辖区'),
      dataIndex: 'businessUnitIdName',
      minWidth: 120,
      align: 'left',
      resizable: true,
      sorter: true,
    },
    {
      title: t('岗位'),
      dataIndex: 'postNames',
      width: 120,
      align: 'left',
      resizable: true,
      sorter: true,
      customRender: ({ record }) => {
        return record.postNames && record.postNames.length > 0 ? record.postNames.join(',') : '';
      },
    },
    {
      title: t('角色'),
      dataIndex: 'roleNames',
      width: 120,
      align: 'left',
      resizable: true,
      sorter: true,
      customRender: ({ record }) => {
        return record.roleNames && record.roleNames.length > 0 ? record.roleNames.join(',') : '';
      },
    },
    {
      title: t('创建时间'),
      dataIndex: 'createDate',
      width: 120,
      align: 'left',
      resizable: true,
      sorter: true,
    },
    {
      title: t('账号状态'),
      dataIndex: 'enabledMark',
      width: 120,
      customRender: ({ record }) => {
        const enable = record.enabledMark;
        const color = enable === 1 ? 'green' : 'red';
        const text = enable === 1 ? '正常' : '锁定';
        return h(Tag, { color: color }, () => text);
      },
      align: 'left',
      resizable: true,
    },
  ];

  export default defineComponent({
    name: 'UserManagement',
    components: { BasicTable, ResizePageWrapper, DeptTree, AccountModal, TableAction },
    setup() {
      const { notification } = useMessage();
      const { hasPermission } = usePermission();

      const selectDeptId = ref('');
      const isVisible = ref<boolean>(false);
      const deleteIndex = ref();
      const [registerModal, { openModal }] = useModal();
      const [registerTable, { reload }] = useTable({
        title: t('用户列表'),
        api: getUserPageList,
        rowKey: 'id',
        columns,
        formConfig: {
          rowProps: {
            gutter: 16,
          },
          schemas: searchFormSchema,
          showResetButton: false,
        },
        beforeFetch: (params) => {
          //发送请求默认新增  左边树结构所选机构id
          return { ...params, departmentId: selectDeptId.value };
        },
        useSearchForm: true,
        showTableSetting: true,
        striped: false,
        actionColumn: {
          width: 150,
          title: t('操作'),
          dataIndex: 'action',
          slots: { customRender: 'action' },
        },
        tableSetting: {
          size: false,
        },
        // customRow: (record) => {
        //   return {
        //     ondblclick: () => {
        //       if (hasPermission('user:edit')) {
        //         handleEdit(record);
        //       }
        //     },
        //   };
        // },
      });

      function popVisible(index) {
        return isVisible.value && index === deleteIndex.value;
      }

      function handleCreate() {
        openModal(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        openModal(true, {
          id: record.id,
          isUpdate: true,
        });
      }

      function handleVisibleChange(index, bool) {
        if (!bool) {
          isVisible.value = false;
          return;
        }
        // if (import.meta.env.VITE_GLOB_PRODUCTION === 'true') {
        //   notification.warning({
        //     message: '在线环境暂不允许该操作，请联系管理员。',
        //   });
        //   return;
        // }
        isVisible.value = true;
        deleteIndex.value = index;
      }

      function handleDelete(record: Recordable) {
        isVisible.value = false;
        deleteUser([record.id]).then((_) => {
          reload();
          notification.success({
            message: t('删除'),
            description: t('成功'),
          }); //提示消息
        });
      }
      function handleCancel() {
        isVisible.value = false;
      }
      function handleReset(record) {
        // if (import.meta.env.VITE_GLOB_PRODUCTION === 'true') {
        //   notification.warning({
        //     message: '在线环境暂不允许该操作，请联系管理员。',
        //   });
        //   return;
        // }
        resetUserPassword(record.id).then(() => {
          notification.success({
            message: t('重置密码成功'),
            description: t('成功'),
          });
        });
      }
      async function handleLock(record) {
        await userEnabled(record.id);
        notification.success({
          message: record.enabledMark ? '锁定成功' : t('解锁成功'),
          description: t('成功'),
        });
        await reload();
      }
      function handleSuccess() {
        reload();
      }

      function handleSelect(deptId = '') {
        selectDeptId.value = deptId;
        reload();
      }

      return {
        registerTable,
        registerModal,
        popVisible,
        handleVisibleChange,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
        handleSelect,
        handleReset,
        handleLock,
        handleCancel,
        t,
      };
    },
  });
</script>
