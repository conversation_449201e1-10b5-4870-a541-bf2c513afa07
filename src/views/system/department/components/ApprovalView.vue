<template>
  <BasicModal
    @register="registerModal"
    title="审批专员配置"
    :width="800"
    fixedHeight
    destroyOnClose
    v-bind="$attrs"
    @ok="handleSubmit"
  >
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'userId'">
          <SelectUser
            :selectedIds="record.userId?.split(',')"
            :multiple="true"
            @change="
              (_, options) => {
                record.userId = options.length ? options.map((x) => x.id).toString() : '';
                record.phoneNumber = options.length === 1 ? options[0].mobile : '';
                record.emailCount = options.length === 1 ? options[0].email : '';
              }
            "
            @change-names="
              (names) => {
                record.userName = names;
              }
            "
          >
            <a-input readonly placeholder="请选择对应人员" v-model:value="record.userName">
              <template #suffix>
                <Icon icon="ant-design:ellipsis-outlined" />
              </template>
            </a-input>
          </SelectUser>
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicTable, useTable, BasicColumn } from '/@/components/Table';
  import { updateApprovalUser, getApprovalUser } from '/@/api/system/department';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { Icon } from '/@/components/Icon';
  import { SelectUser } from '/@/components/SelectOrganizational/index';

  const { t } = useI18n();

  const columns: BasicColumn[] = [
    {
      title: '专员名称',
      dataIndex: 'approvalSpecialistName',
    },
    {
      title: '对应人员',
      dataIndex: 'userId',
    },
    {
      title: '手机号码',
      dataIndex: 'phoneNumber',
    },
    {
      title: '邮箱',
      dataIndex: 'emailCount',
    },
  ];
  defineEmits(['register']);
  const { notification } = useMessage();
  const deptId = ref('');

  const [registerTable, { getDataSource }] = useTable({
    api: getApprovalUser,
    beforeFetch: () => {
      return deptId.value;
    },
    rowKey: 'id',
    columns,
    showIndexColumn: false,
    pagination: false,
    bordered: true,
  });

  const [registerModal, { closeModal }] = useModalInner(async (info) => {
    deptId.value = info.id;
  });

  const handleSubmit = async () => {
    await updateApprovalUser(getDataSource());
    notification.success({
      message: t('提示'),
      description: t('配置成功'),
    });
    closeModal();
  };
</script>
<style lang="less" scoped>
  :deep(.ant-input-affix-wrapper) {
    border: none !important;
  }
</style>
