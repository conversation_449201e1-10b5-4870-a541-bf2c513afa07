<template>
  <div>
    <a-tabs v-model:activeKey="activeKey" size="large">
      <a-tab-pane key="1" :tab="t('表单页面')">
        <CodeEditor :mode="MODE.VUE" :value="codes?.formCode" />
      </a-tab-pane>
      <a-tab-pane key="2" :tab="t('列表页面')">
        <CodeEditor :mode="MODE.VUE" :value="codes?.listCode" />
      </a-tab-pane>
      <a-tab-pane key="3" :tab="t('Api代码')">
        <CodeEditor :mode="MODE.JS" :value="codes?.apiCode" />
      </a-tab-pane>
      <a-tab-pane key="4" :tab="t('TS类型')">
        <CodeEditor :mode="MODE.JS" :value="codes?.modelCode" />
      </a-tab-pane>
      <a-tab-pane key="5" :tab="t('Config代码')">
        <CodeEditor :mode="MODE.JS" :value="codes?.configJsonCode" />
      </a-tab-pane>
      <a-tab-pane key="6" :tab="t('实体类')">
        <a-tabs v-model:activeKey="entityActiveKey">
          <a-tab-pane :key="index" :tab="key" v-for="(code, key, index) in codes?.entityCode">
            <CodeEditor :mode="MODE.VUE" :value="code" />
          </a-tab-pane>
        </a-tabs>
      </a-tab-pane>
      <a-tab-pane key="7" :tab="t('控制器')">
        <CodeEditor :mode="MODE.JS" :value="codes?.controllerCode" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script lang="ts" setup>
  import { inject, Ref, ref, watch } from 'vue';
  import { CodeEditor, MODE } from '/@/components/CodeEditor';
  import { TableInfo } from '/@/components/Designer';
  import { FormProps } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { ShowFrontCodeModel } from '/@/model/generator/codeGenerator';
  import { GeneratorConfig } from '/@/model/generator/generatorConfig';
  import { buildOption } from '/@/utils/helper/designHelper';
  import { buildCode } from '/@/utils/helper/generatorHelper';
  import { dataFirstPreviewCode, codeFirstPreviewCode } from '/@/api/system/generator';
  import { upperFirst } from 'lodash-es';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = defineProps({
    //是否是自定义表单生成代码
    isFormGenerator: {
      type: Boolean,
      default: false,
    },
  });

  const generatorConfig = inject<GeneratorConfig>('generatorConfig') as GeneratorConfig;
  const tableInfo = inject<Ref<TableInfo[]>>('tableInfo', []);
  const current = inject<Ref<number>>('current') as Ref<number>;
  const designType = inject<string>('designType', '');

  const { notification } = useMessage();

  const activeKey = ref('1');
  const entityActiveKey = ref(0);
  const isGetCodes = ref<boolean>(false);
  const codes = ref<ShowFrontCodeModel>();

  watch(
    () => current.value,
    (val) => {
      //如果已经到此步骤 则生成代码显示
      if (val === 4 || (props.isFormGenerator && val === 2)) {
        codes.value = buildCode(
          generatorConfig,
          tableInfo.value,
          buildOption(generatorConfig.formJson) as FormProps,
        );

        previewCode();
      }
    },
  );

  //获取实体类和控制器的代码
  const previewCode = async () => {
    let data;
    if (designType === 'data' || props.isFormGenerator) {
      data = await dataFirstPreviewCode(generatorConfig);
    } else {
      data = await codeFirstPreviewCode(generatorConfig);
    }
    const className = generatorConfig.outputConfig.className;
    const controller = upperFirst(`${className}Controller`);
    codes.value!.controllerCode = data.controllerCode[controller];
    codes.value!.entityCode = data.entityCode;

    isGetCodes.value = true;
  };

  //验证当前步骤的数据
  const validateStep = async (): Promise<boolean> => {
    if (!isGetCodes.value) {
      setTimeout(() => {
        validateStep();
      }, 100);
      return false;
    }

    if (!codes.value?.apiCode || codes.value?.apiCode.length === 0) {
      notification.error({
        message: t('提示'),
        description: t(`Api代码生成错误，请联系管理员！`),
      }); //提示消息
      return false;
    }
    if (!codes.value?.modelCode || codes.value?.modelCode.length === 0) {
      notification.error({
        message: t('提示'),
        description: t(`TS类型生成错误，请联系管理员！`),
      }); //提示消息
      return false;
    }
    if (!codes.value?.formCode || codes.value?.formCode.length === 0) {
      notification.error({
        message: t('提示'),
        description: t(`表单页面代码生成错误，请联系管理员！`),
      }); //提示消息
      return false;
    }
    if (!codes.value?.listCode || codes.value?.listCode.length === 0) {
      notification.error({
        message: t('提示'),
        description: t(`列表页面代码生成错误，请联系管理员！`),
      }); //提示消息
      return false;
    }
    if (!codes.value?.configJsonCode || codes.value?.configJsonCode.length === 0) {
      notification.error({
        message: t('提示'),
        description: t(`Config配置代码生成错误，请联系管理员！`),
      }); //提示消息
      return false;
    }
    if (!codes.value?.controllerCode || codes.value?.controllerCode.length === 0) {
      notification.error({
        message: t('提示'),
        description: t(`控制器代码生成错误，请联系管理员！`),
      }); //提示消息
      return false;
    }
    if (!codes.value?.entityCode || Object.keys(codes.value?.entityCode).length === 0) {
      notification.error({
        message: t('提示'),
        description: t(`实体类代码生成错误，请联系管理员！`),
      }); //提示消息
      return false;
    }
    return true;
  };

  defineExpose({ validateStep });
</script>
