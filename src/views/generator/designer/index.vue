<template>
  <div style="height: 100%">
    <DesignForm ref="designFormRef" />
    <FormPreviewDrawer @register="registerDrawer" />
  </div>
</template>
<script lang="ts" setup>
  import { DesignForm } from '/@/components/Designer';
  import FormPreviewDrawer from './components/FormPreviewDrawer.vue';
  import { ref } from 'vue';
  import { useDrawer } from '/@/components/Drawer';

  const designFormRef = ref();

  //注册抽屉 获取外部操作抽屉得方法
  const [registerDrawer] = useDrawer();
</script>
