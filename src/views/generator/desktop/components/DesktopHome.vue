<template>
  <div class="content-box" v-if="list.length > 0 && data.show" :key="data.renderKey">
    <grid-layout
      :layout="list"
      :col-num="12"
      :row-height="30"
      :is-draggable="false"
      :is-resizable="false"
      :is-mirrored="false"
      :vertical-compact="false"
      :margin="[10, 10]"
      :use-css-transforms="false"
    >
      <grid-item
        v-for="(item, index) in list"
        :key="index"
        :x="item.x"
        :y="item.y"
        :w="item.w"
        :h="item.h"
        :i="item.i"
        :maxH="item.maxH"
        :minH="item.minH"
        :maxW="item.maxW"
        :minW="item.minW"
      >
        <component
          :id="item.i"
          :w="item.w"
          :h="item.h"
          :type="item.type"
          :config="item.config"
          :title="item.config.title"
          :changeDataKey="data.changeDataKey"
          :echarts="item.config.echarts ? item.config.echarts : null"
          class="item-box"
          :is="getLegendComponent(item.type)"
          :isMenu="isMenu"
        />
      </grid-item>
    </grid-layout>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, onUnmounted, provide, reactive } from 'vue';
  import { DesktopInfoItem } from '/@/model/desktop/designer';
  import { DesktopComponent } from '/@/enums/desktop';
  import useDesktopComponent from '../hooks/useDesktopComponent';
  defineEmits(['close']);
  withDefaults(
    defineProps<{
      list: Array<DesktopInfoItem>;
      isMenu: boolean;
    }>(),
    {
      list: () => {
        return [];
      },
      isMenu: false,
    },
  );

  const { componentByType } = useDesktopComponent();
  const data: {
    show: boolean;
    renderKey: number;
    changeDataKey: number;
  } = reactive({
    renderKey: 0,
    changeDataKey: 0,
    show: false,
  });
  provide('changeDataDisplay', changeDataDisplay);
  onMounted(() => {
    window.addEventListener('resize', () => {
      resetDisplay();
    });
    window.localStorage.setItem('TableRecord', '');
    data.show = true;
  });
  onUnmounted(() => {
    window.removeEventListener('resize', () => {
      resetDisplay();
    });
  });
  function changeDataDisplay() {
    data.changeDataKey++;
  }
  function resetDisplay() {
    data.renderKey++;
  }
  function getLegendComponent(type: DesktopComponent) {
    return componentByType.has(type)
      ? componentByType.get(type)
      : componentByType.get(DesktopComponent.DEFAULT);
  }
</script>

<style lang="less" scoped>
  .content-box {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: auto;
    padding-bottom: 60px;
  }

  .vue-grid-layout {
    width: 100%;
    height: 100%;
  }

  .item-box {
    position: relative;
    background-color: #fff;
    width: 100%;
    height: 100%;
    min-width: 10px;
    min-height: 10px;
    box-shadow: 0 3px 6px 1px rgb(0 0 0 / 16%);
    border-radius: 4px;
  }

  html[data-theme='dark'] {
    .item-box {
      background-color: #171616;
    }
  }

  .empty-box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .empty {
      width: 480px;
    }
  }

  :deep(.delete-icon) {
    cursor: pointer;
    color: @clear-color;
  }

  :deep(.edit-icon) {
    cursor: pointer;
    color: @primary-color;
    margin-right: 10px;
  }
</style>
<style>
  html[data-theme='dark'] {
    .item-title {
      color: #fff !important;
    }

    .item-title::after {
      background-color: #3b3737 !important;
    }
  }
</style>
