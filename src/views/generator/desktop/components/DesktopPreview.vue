<template>
  <DesktopLayout class="preview-box">
    <template #head>
      <DesignHead :title="t('设计预览')">
        <template #buttons>
          <div class="design-button">
            <a-button type="primary" class="clean-icon" @click="$emit('close')">{{
              t('关闭')
            }}</a-button>
          </div></template
        >
      </DesignHead>
    </template>
    <DesktopHome :list="props.list" :isMenu="false" />
  </DesktopLayout>
</template>

<script setup lang="ts">
  import DesktopHome from './DesktopHome.vue';
  import DesignHead from './designer/layout/Head.vue';
  import DesktopLayout from './designer/layout/Desktop.vue';
  import { DesktopInfoItem } from '/@/model/desktop/designer';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  defineEmits(['close']);
  let props = withDefaults(
    defineProps<{
      list: Array<DesktopInfoItem>;
    }>(),
    {
      list: () => {
        return [];
      },
    },
  );
</script>

<style lang="less" scoped></style>
