<template>
  <div class="box">
    <div class="item-title">
      <span>{{ props.title }}</span>
      <router-link v-if="config.jumpId" :to="config.path">{{ t('更多') }}</router-link>
    </div>
    <EmptyBox v-if="props.config.apiData.length == 0 || props.config.columns.length == 0" />
    <div class="list-box" v-else>
      <div class="row" v-for="(item, index) in props.config.apiData" :key="index">
        <div
          class="item"
          :class="' align-' + item2.align + ' '"
          :style="'flex-basis: ' + item2.width + '%;'"
          v-for="(item2, index2) in props.config.columns"
          :key="index2"
        >
          {{ item[item2.id] }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { informationProperties } from '../config/properties';
  import { DesktopComponent } from '/@/enums/desktop';
  import { InformationConfig } from '/@/model/desktop/designer';
  import { EmptyBox } from '/@/components/ModalPanel/index';
  import { nextTick, onMounted, ref, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { isNil } from 'lodash-es';
  import useApiRequest from '/@/hooks/event/useApiRequest';
  const { changeApiOptions } = useApiRequest();
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      type: DesktopComponent;
      w: number;
      h: number;
      title: string;
      config: InformationConfig;
    }>(),
    {
      type: DesktopComponent.DEFAULT,
      w: 0,
      h: 0,
      config: () => {
        return informationProperties;
      },
    },
  );
  const emit = defineEmits(['update:config']);
  const configList = ref();
  watch(
    () => props.config.renderKey,
    (val) => {
      val && resizeLayout();
    },
    {
      deep: true,
    },
  );
  watch(
    () => props.config.maxRows,
    () => {
      getApiData();
    },
  );
  async function resizeLayout() {
    await nextTick();
    await changeData();
  }
  onMounted(async () => {
    await nextTick();
    await changeData();
  });
  async function changeData() {
    let config = props.config;
    if (config.apiConfig.path) {
      let res = await changeApiOptions(config.apiConfig);
      if (res.list && Array.isArray(res.list)) {
        configList.value = res.list;
        getApiData();
      }
      if (res.columns && Array.isArray(res.columns)) config.apiColumns = res.columns;
      emit('update:config', config);
    }
  }
  function getApiData() {
    const config = props.config;
    const endIndex = isNil(props.config.maxRows) ? configList.value.length : props.config.maxRows;
    config.apiData = configList.value.slice(0, endIndex) || [];
    emit('update:config', config);
  }
</script>

<style lang="less" scoped>
  .align-left {
    justify-content: flex-start;
  }

  .align-center {
    justify-content: center;
  }

  .align-right {
    justify-content: flex-end;
  }

  .box {
    padding-top: 40px;

    .item-title {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      color: #262626;
      font-size: 14px;
      padding: 0 16px;
      display: flex;
      justify-content: space-between;

      .more {
        color: #5e95ff;
      }
    }

    .item-title::after {
      content: '';
      display: block;
      position: absolute;
      height: 1px;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #f0f0f0;
    }

    .list-box {
      .row {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #f0f0f0;
        color: #595959;
        font-size: 14px;
        height: 30px;

        .item {
          display: flex;
          padding: 4px 10px;
        }
      }
    }
  }
</style>
