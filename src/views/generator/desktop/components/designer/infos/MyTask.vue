<template>
  <div class="box" v-if="data.show">
    <div class="item-title">{{ props.title }}</div>
    <div v-if="data.list.length > 0" class="menu-box">
      <div class="menu-item" v-for="(item, index) in data.list" :key="index">
        <div class="menu-icon"
          ><div class="bg" :style="'background-color:' + item.color + ''"></div>
          <Icon :icon="item.icon" :size="36" :color="item.color" class="icon"
        /></div>
        <div class="menu-title"
          ><span class="num">{{ item.count }}</span>
          <template v-if="isMenu && index == 0">
            <router-link
              class="opr"
              :to="{
                path: '/task/processtasks',
                query: { name: 'ToDoTasks' },
              }"
            >
              <span class="name">{{ item.name }}</span>
            </router-link></template
          ><template v-else-if="isMenu && index == 1">
            <router-link
              class="opr"
              :to="{
                path: '/task/processtasks',
                query: { name: 'ToDoTasks' },
              }"
            >
              <span class="name">{{ item.name }}</span>
            </router-link></template
          >
          <template v-else-if="isMenu && index == 2">
            <router-link
              class="opr"
              :to="{
                path: '/task/processtasks',
                query: { name: 'TaskDone' },
              }"
            >
              <span class="name">{{ item.name }}</span>
            </router-link></template
          >
          <span v-else class="name">{{ item.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, reactive } from 'vue';
  import Icon from '/@/components/Icon/index';
  import { myTaskProperties } from '../config/properties';
  import { DesktopComponent } from '/@/enums/desktop';
  import { DesktopConfig } from '/@/model/desktop/designer';
  import { getMyTaskCount } from '/@/api/desktop';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      type: DesktopComponent;
      w: number;
      h: number;
      title: string;
      config: DesktopConfig;
      isMenu: boolean;
    }>(),
    {
      type: DesktopComponent.DEFAULT,
      w: 0,
      h: 0,
      title: '',
      config: () => {
        return myTaskProperties;
      },
      isMenu: false,
    },
  );
  const data: {
    show: boolean;
    list: Array<{
      icon: string;
      name: string;
      color: string;
      count: number;
    }>;
  } = reactive({
    show: false,
    list: [
      {
        icon: 'ant-design:container-twotone',
        name: t('待办任务'),
        count: 34,
        color: '#ff0033',
      },
      {
        icon: 'ant-design:hdd-outlined',
        name: t('委托任务'),
        count: 0,
        color: '#ff880e',
      },
      {
        icon: 'ant-design:file-search-outlined',
        name: t('已办任务'),
        count: 0,
        color: '#6fc86f',
      },
    ],
  });
  onMounted(async () => {
    try {
      let res = await getMyTaskCount();
      data.list = [
        {
          icon: 'ant-design:container-twotone',
          name: t('待办任务'),
          count: res.pendingCount,
          color: '#ff0033',
        },
        {
          icon: 'ant-design:hdd-outlined',
          name: t('委托任务'),
          count: res.delegateCount,
          color: '#ff880e',
        },
        {
          icon: 'ant-design:file-search-outlined',
          name: t('已办任务'),
          count: res.finishedCount,
          color: '#6fc86f',
        },
      ];
    } catch (error) {}
    data.show = true;
  });
</script>

<style lang="less" scoped>
  .box {
    padding-top: 40px;

    .item-title {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      color: #262626;
      font-size: 14px;
      padding-left: 16px;
    }

    .item-title::after {
      content: '';
      display: block;
      position: absolute;
      height: 1px;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #f0f0f0;
    }

    .item {
      width: 100%;
      height: 100%;
    }
  }

  .menu-box {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 10px;

    .menu-item {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 8px;

      .menu-icon {
        position: relative;
        height: 64px;
        display: flex;
        justify-content: center;
        align-items: center;

        .bg {
          border-radius: 50%;
          opacity: 0.1;
          height: 64px;
          width: 64px;
          position: relative;
        }

        .icon {
          position: absolute;
          top: 0;
          left: 0;
          height: 64px;
          width: 64px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .menu-title {
        color: #262626;
        font-size: 14px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-left: 8px;

        .num {
          font-size: 24px;
          color: #262626;
          line-height: 42px;
          font-weight: 700;
        }

        .name {
          color: #262626;
          font-size: 14px;
        }
      }
    }
  }
</style>
