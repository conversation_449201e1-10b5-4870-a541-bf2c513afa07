<template>
  <a-select v-model:value="data.value" style="width: 100%" allowClear @change="changeValue">
    <a-select-option v-for="(item, index) in props.apiColumns" :key="index" :value="item.prop">{{
      item.label
    }}</a-select-option>
  </a-select>
</template>

<script setup lang="ts">
  import { reactive, watch } from 'vue';
  const props = withDefaults(
    defineProps<{
      value: string;
      apiColumns: Array<{ prop: string; label: string }>;
    }>(),
    {
      value: '',
    },
  );

  const emit = defineEmits(['update:value', 'change']);
  const data = reactive({
    value: props.value,
  });

  watch(
    () => props.value,
    (val) => {
      data.value = val;
    },
  );
  function changeValue() {
    emit('update:value', data.value);
    emit('change');
  }
</script>

<style scoped></style>
