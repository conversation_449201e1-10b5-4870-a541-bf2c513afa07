<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="t('脚本配置')"
    @ok="handleSubmit"
    @visible-change="visibleChange"
    width="70%"
  >
    <div class="config-box">
      <div>
        <div class="box-top">
          <div class="title">{{ t('脚本示例') }}</div>
        </div>
        <div>
          <div
            v-for="(item, index) in example"
            :key="index"
            :class="['script-name', { 'active-name': activeIndex === index }]"
            @click="activeIndex = index"
          >
            {{ item.title }}
          </div>
        </div>
      </div>
      <div style="max-width: 500px">
        <div class="box-top">
          <div class="title">{{ t('自定义脚本') }}</div>
        </div>
        <div style="height: calc(100% - 40px)">
          <CodeEditor v-model:value="definedScript" language="js" />
        </div>
      </div>
      <div>
        <div class="box-top">
          <div class="title">{{ t('脚本示例代码') }}</div>
        </div>
        <div style="height: calc(100% - 40px)">
          <CodeEditor :value="example[activeIndex].code" language="js" readonly />
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { CodeEditor } from '/@/components/CodeEditor';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const definedScript = ref(``);
  const emit = defineEmits(['success', 'register']);
  const activeIndex = ref(0);
  const componentVisible = ref<boolean>(false);

  const example = reactive([
    {
      title: t('ajax加载服务器数据'),
      code: `formActionType.httpRequest({
      requestType: 'get', //请求方式有: get、post、put、delete
      requestUrl: '/system/dictionary-detail', //请求地址
      params: {
        itemId: '1419276800524423168'
      },//请求参数
      errorMessageMode: 'none' //错误提示方式，默认为none
  })
        // errorMessageMode='message' 错误提示为消息提示
        // errorMessageMode='modal' 显示modal错误弹窗，用于一些比较重要的错误
        // errorMessageMode='none'  一般是调用时明确表示不希望自动弹出错误提示
    `,
    },
    {
      title: t('弹出对话框'),
      code: `formActionType.showModal({
      title: '我是标题', // 对话框标题
      content: '我是内容', // 对话框内容
      onOk() {
        console.log('onOk') // 点击确定按钮的处理逻辑
      },
      onCancel() {
        console.log('onCancel') // 点击取消按钮的处理逻辑
      },
  });`,
    },
    {
      title: t('if判断'),
      code: `const value = 3
  if(value === 3){ /* 括号内为判断条件 */
    console.log('value为3')
    /* 处理逻辑 */
  }`,
    },
    {
      title: t('循环'),
      code: `for(let i=0; i<3; i++){ /* 括号内为循环条件 */
    console.log('i的值为', i)
    /* 处理逻辑 */
  }`,
    },
    {
      title: t('正则校验'),
      code: `formActionType.regTest({
      regExpression: /^[0-9]/,// 正则表达式
      testValue: 123,// 需要进行校验的值
      successMessage: '校验成功',// 校验成功的提示信息
      errorMessage: '校验失败'// 校验失败的提示信息
  })`,
    },
    {
      title: t('刷新API'),
      code: `formActionType.refreshAPI()`,
    },
  ]);
  const [registerModal, { closeModal, setModalProps }] = useModalInner(async (data) => {
    //如果已经设置过当前事件代码  则显示已经设置的值 反之  显示默认值
    definedScript.value = data.content;
    setModalProps({
      fixedHeight: true,
    });
  });
  const handleSubmit = () => {
    emit('success', definedScript.value);
    closeModal();
  };

  const visibleChange = (visible: boolean) => {
    if (!visible) componentVisible.value = false;
  };
</script>

<style lang="less" scoped>
  .config-box {
    display: flex;
    height: 100%;

    div:nth-child(1) {
      flex: 1;
    }

    div {
      flex: 2;
    }

    .box-top {
      display: flex;
      justify-content: space-between;
      height: 30px;
      border-bottom: 1px solid #f0f0f0;
      margin: 0 0 10px 10px;
    }

    .script-name {
      cursor: pointer;
      padding: 10px 0 10px 15px;

      &:hover {
        background: #eef4ff;
      }
    }

    .active-name {
      background: #eef4ff;
    }
  }

  :deep(.ant-drawer-title),
  .title {
    font-size: 16px;
    height: 20px;
    line-height: 18px;
    padding-left: 6px;
    border-left: 6px solid #5e95ff;
  }

  :deep(.ant-tag) {
    padding: 8px;
    font-size: 13px;
    margin-bottom: 7px;
    cursor: move;
  }
</style>
