<template>
  <Box v-if="data.show">
    <a-collapse v-model:activeKey="activeKey" ghost expandIconPosition="right">
      <a-collapse-panel key="1" :header="t('基础配置')">
        <a-form-item :label="t('标题')" :colon="false" labelAlign="left">
          <a-input v-model:value="data.info.config.title" />
        </a-form-item>
        <a-form-item :label="t('最大行数')" :colon="false" labelAlign="left">
          <a-input-number
            v-model:value="data.info.config.maxRows"
            :min="0"
            :max="10"
            @change="resetDisplay"
          />
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="2" :header="t('大小定位')">
        <Location v-model:info="data.info" />
      </a-collapse-panel>
    </a-collapse>
  </Box>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref, watch } from 'vue';
  import Box from './Box.vue';
  import { todoListInfo } from '../config/info';
  import { TodoListInfo } from '/@/model/desktop/designer';
  import Location from './collapse/Location.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      info: TodoListInfo;
    }>(),
    {
      info: () => {
        return todoListInfo;
      },
    },
  );
  watch(
    () => props.info,
    (val) => {
      if (val) data.info = val;
    },
    {
      deep: true,
    },
  );
  const data: {
    show: boolean;
    info: TodoListInfo;
  } = reactive({
    show: true,
    info: todoListInfo,
  });
  const activeKey = ref(['1', '2']);
  onMounted(() => {
    data.info = props.info;
    data.show = true;
  });
  function resetDisplay() {
    if (data.info.config.renderKey >= 0) {
      data.info.config.renderKey++;
    }
  }
</script>

<style lang="less" scoped></style>
