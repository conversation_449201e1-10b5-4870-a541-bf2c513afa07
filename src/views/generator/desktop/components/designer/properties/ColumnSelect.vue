<template>
  <InputModel
    :value="title"
    :placeholder="title"
    style="width: 100%; min-width: 100px"
    @click="open"
  />
  <a-modal
    :width="1000"
    v-model:visible="state.visible"
    :title="title"
    :maskClosable="false"
    @ok="handleSubmit"
    @cancel="handleClose"
    :bodyStyle="{ padding: '0 10px 10px' }"
  >
    <div class="list-title">{{ t('字段列表') }}</div>
    <a-row type="flex" align="middle" :gutter="12" style="margin: 15px 0">
      <a-col flex="auto" class="text-right">{{ t('调用接口：') }}</a-col>
      <a-col flex="75%">
        <a-input v-model:value="path" disabled />
      </a-col>
      <a-col flex="auto">
        <a-button key="submit" type="primary" @click="interfaceInfoOpen">{{
          t('查看接口信息')
        }}</a-button>
      </a-col>
    </a-row>
    <a-table
      :dataSource="state.outputParams"
      :columns="preloadColumns"
      :pagination="false"
      :scroll="{ y: '400px' }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'dataIndex'">
          <a-input v-model:value="record.dataIndex" :placeholder="t('请填写字段名')" />
        </template>
        <template v-if="column.key === 'title'">
          <a-input v-model:value="record.title" :placeholder="t('请填写表头名称')" />
        </template>
      </template>
    </a-table>
    <a-button type="dashed" block @click="addOutPutParams">
      {{ t('新增') }}
    </a-button>
  </a-modal>
  <!-- 查看接口信息 -->
  <a-modal
    :width="1000"
    v-model:visible="state.interfaceInfo.visible"
    :title="t('查看接口信息')"
    :maskClosable="false"
    @ok="interfaceInfoClose"
    @cancel="interfaceInfoClose"
    :bodyStyle="{ padding: '0 10px 10px' }"
  >
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="1" :tab="t('接口信息')">
        <CodeEditor :value="scriptStr" language="json" readonly />
      </a-tab-pane>
      <a-tab-pane key="2" :tab="t('接口出参示例')">
        <CodeEditor :value="infoExample" language="json" readonly />
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<script setup lang="ts">
  import { computed, reactive, ref } from 'vue';
  import { InputModel } from '/@/components/ApiConfig';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { CodeEditor } from '/@/components/CodeEditor';
  import { cloneDeep } from 'lodash-es';
  interface outputItem {
    dataIndex: string;
    title: string;
  }
  const { t } = useI18n();
  const title = t('表头配置-API');
  const props = defineProps({
    apiConfig: {
      type: Object as PropType<any>,
    },
    apiColumn: Array<outputItem>,
  });
  let emits = defineEmits(['update:apiColumn']);
  const path = computed(() => {
    return props.apiConfig && props.apiConfig.path ? props.apiConfig.path : '';
  });
  const scriptStr = computed(() => {
    return props.apiConfig && props.apiConfig.script ? props.apiConfig.script : '';
  });
  const activeKey = ref('1');
  const state: {
    visible: boolean;
    interfaceInfo: {
      visible: boolean;
    };
    outputParams: Array<outputItem>;
  } = reactive({
    interfaceInfo: {
      visible: false,
    },
    visible: false,
    outputParams: [],
  });
  const preloadColumns = [
    {
      title: '#',
      align: 'center',
      customRender: ({ index }) => `${index + 1}`, // 显示每一行的序号
      width: 50,
    },
    {
      title: t('接口返回参数字段名'),
      dataIndex: 'dataIndex',
      key: 'dataIndex',
      align: 'center',
    },
    {
      title: t('表头名称'),
      dataIndex: 'title',
      key: 'title',
      align: 'center',
    },
  ];
  function open() {
    state.visible = true;
    if (props.apiColumn) state.outputParams = cloneDeep(props.apiColumn);
  }
  function handleSubmit() {
    emits('update:apiColumn', state.outputParams);
    state.visible = false;
  }
  function handleClose() {
    state.visible = false;
  }
  function addOutPutParams() {
    state.outputParams.push({
      dataIndex: '',
      title: '',
    });
  }
  function interfaceInfoOpen() {
    state.interfaceInfo.visible = true;
  }
  function interfaceInfoClose() {
    state.interfaceInfo.visible = false;
  }
  const infoExample = JSON.stringify({
    code: 0,
    msg: t('提示信息'),
    data: [
      {
        label: t('选项一'),
        value: 1,
      },
      {
        label: t('选项二'),
        value: 1,
      },
      {
        label: t('选项三'),
        value: 1,
      },
    ],
  });
</script>

<style scoped></style>
