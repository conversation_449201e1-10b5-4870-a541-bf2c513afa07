<template>
  <div class="box">
    <div class="item-title">{{ props.title }}</div>
    <div ref="chartRef" class="item"></div>
  </div>
</template>
<script lang="ts" setup>
  import * as echarts from 'echarts';
  import { ref, nextTick, unref, onMounted, markRaw, watch } from 'vue';
  import { DesktopComponent } from '/@/enums/desktop';
  import { chartProperties } from '../config/properties';
  import { isNumber } from 'min-dash';
  import { useI18n } from '/@/hooks/web/useI18n';
  import useApiRequest from '/@/hooks/event/useApiRequest';
  const { changeApiOptions } = useApiRequest();
  const { t } = useI18n();
  const chartRef = ref<HTMLDivElement>();
  const props = withDefaults(
    defineProps<{
      type: DesktopComponent;
      w: number;
      h: number;
      title: string;
      config: any;
    }>(),
    {
      type: DesktopComponent.DEFAULT,
      w: 0,
      h: 0,
      title: '',
      config: () => {
        return chartProperties;
      },
    },
  );
  const emit = defineEmits(['update:config']);
  const myEcharts = ref<any>();
  onMounted(async () => {
    try {
      await nextTick();
      await changeData();
      myEcharts.value = markRaw(echarts.init(unref(chartRef) as HTMLDivElement));
      initChart();
      setTimeout(() => {
        resizeChart();
      }, 1);
    } catch (error) {
      console.log('error: ', error);
    }
  });
  watch(
    () => props.w,
    (val) => {
      val && resizeChart();
    },
    {
      deep: true,
    },
  );
  watch(
    () => props.h,
    (val) => {
      val && resizeChart();
    },
    {
      deep: true,
    },
  );
  watch(
    () => props.config.renderKey,
    (val) => {
      val && resizeLayout();
    },
    {
      deep: true,
    },
  );
  async function resizeLayout() {
    try {
      await nextTick();
      await changeData();
      initChart(true);
      setTimeout(() => {
        resizeChart();
      }, 1);
    } catch (error) {
      console.log('error: ', error);
    }
  }
  async function changeData() {
    try {
      if (props.config.apiConfig.path) {
        let config = props.config;
        let res = await changeApiOptions(props.config.apiConfig);
        if (res) {
          config.apiData = res;
          config.apiColumns = Object.keys(res);
        }
        if (res.list && Array.isArray(res.list)) {
          config.apiData = res.list;
        }
        if (res.columns && Array.isArray(res.columns)) config.apiColumns = res.columns;
        emit('update:config', config);
        changeApiData();
      }
    } catch (error) {
      console.log('error: ', error);
    }
  }
  function changeApiData() {
    let config = props.config;
    // 柱状图
    if (props.type == DesktopComponent.CHARTBAR) {
    }
    // 漏斗图
    if (props.type == DesktopComponent.FUNNEL) {
      let labelKey = 'name';
      if (config.labelKey) {
        labelKey = config.labelKey;
      } else {
        config.labelKey = 'name';
      }
      let valueKey = 'value';
      if (config.labelKey) {
        valueKey = config.valueKey;
      } else {
        config.valueKey = 'value';
      }
      config.echarts.legend.width = config.autoWidth
        ? 'auto'
        : config.echarts.legend.width === 'auto'
        ? 200
        : config.echarts.legend.width;
      if (props.config.echarts && config.echarts.series) {
        config.echarts.series[0].data = config.apiData.map((ele) => {
          return { name: ele[labelKey], value: ele[valueKey] };
        });
      }
      // 图例
      config.echarts.legend.data = config.echarts.series[0].data.map((ele) => {
        return ele.name;
      });
    }
    // 饼图
    if (props.type == DesktopComponent.PIE) {
      config.echarts.legend.width = config.autoWidth
        ? 'auto'
        : config.echarts.legend.width === 'auto'
        ? 200
        : config.echarts.legend.width;
      let labelKey = config.labelKey ? config.labelKey : 'name';
      let valueKey = config.valueKey ? config.valueKey : 'value';
      if (props.config.echarts && config.echarts.series) {
        config.echarts.series[0].data = config.apiData.map((ele, idx) => {
          return {
            name: ele[labelKey],
            value: ele[valueKey],
            selected: idx === config.defaultSelect,
          };
        });
      }
    }
    // 仪表盘
    if (props.type == DesktopComponent.GAUGE) {
      let labelKey = config.labelKey ? config.labelKey : 'name';
      let valueKey = config.valueKey ? config.valueKey : 'value';
      if (config.echarts && config.echarts.series && config.echarts.series[0]) {
        // 仪表盘只显示第一条数据
        config.echarts.series[0].data = config.apiData
          .filter((_, i) => i === 0)
          .map((ele) => {
            return { name: ele[labelKey], value: isNumber(ele[valueKey]) ? ele[valueKey] : 0 };
          });
      }
    }
    // 雷达图
    if (props.type == DesktopComponent.RADAR) {
      // 根据指标来显示数据
      let legendData = [];
      if (props.config.apiData.length > 0) {
        legendData = config.apiData.map((ele) => {
          return ele[config.labelKey];
        });
        if (config.echarts && config.echarts.radar && config.echarts.radar.indicator) {
          config.echarts.radar.indicator.forEach((element, index) => {
            config.echarts.series[0].data[index].value = [];
            config.echarts.series[0].data[index].name = '';
            if (
              element.value &&
              config.echarts.series[0].data &&
              config.echarts.series[0].data[index]
            ) {
              let values = [];
              if (config.apiData.length > 0) {
                values = config.apiData.map((ele) => {
                  return ele[element.value];
                });
              }
              config.echarts.series[0].data[index].value = values;
              config.echarts.series[0].data[index].name = legendData[index];
              config.echarts.legend.data[index] = legendData[index];
            }
          });
        }
      }
    }
    // 甘特图
    if (props.type == DesktopComponent.CATEGORY_STACK) {
      let labelKey = config.labelKey ? config.labelKey : 'name';
      let valueKey = config.valueKey ? config.valueKey : 'value';
      let targetKey = config.targetKey ? config.targetKey : 'value';
      config.echarts.yAxis.data = config.apiData.map((ele) => {
        return ele[labelKey];
      });
      if (props.config.echarts && config.echarts.series) {
        let valueData = config.apiData.map((ele) => {
          return ele[valueKey];
        });
        let valueNameData = config.apiColumns.filter((ele) => {
          return ele.prop == valueKey;
        });
        config.echarts.series[0] = {
          name: valueNameData && valueNameData.length > 0 ? valueNameData[0].label : valueKey,
          type: 'bar',
          stack: 'total',
          label: {
            show: false,
          },
          color: '#ff7345',
          emphasis: {
            focus: 'series',
          },
          data: valueData,
        };
        let targetData = config.apiData.map((ele) => {
          return ele[targetKey];
        });
        let targetNameData = config.apiColumns.filter((ele) => {
          return ele.prop == targetKey;
        });
        config.echarts.series[1] = {
          name: targetNameData && targetNameData.length > 0 ? targetNameData[0].label : targetKey,
          type: 'bar',
          stack: 'total',
          label: {
            show: false,
          },
          color: '#6a6e88',
          emphasis: {
            focus: 'series',
          },
          data: targetData,
        };
      }
    }
    emit('update:config', config);
  }
  /**
   * 初始化echart
   * @param clearCaching 是否清除缓存
   */
  const initChart = (clearCaching = false) => {
    myEcharts.value.showLoading({
      text: t('加载中'),
      color: '#5e95ff',
      textColor: 'rgba(255, 255, 255, 0.6)',
      maskColor: 'rgba(255, 255, 255, 0.2)',
      zlevel: 0,
    });
    setTimeout(() => {
      myEcharts.value.hideLoading();
      if (myEcharts.value) myEcharts.value.setOption(props.config.echarts, clearCaching);
    }, 100);
  };

  /**
   * 重置echart
   */
  const resizeChart = () => {
    if (props.config.echarts) {
      if (myEcharts.value) myEcharts.value.resize();
    }
  };
</script>
<style lang="less" scoped>
  .box {
    width: 100%;
    height: 100%;
    padding-top: 40px;

    .item-title {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      color: #262626;
      font-size: 14px;
      padding-left: 16px;
    }

    .item-title::after {
      content: '';
      display: block;
      position: absolute;
      height: 1px;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #f0f0f0;
    }

    .item {
      height: 100%;
      width: 100%;
      -webkit-tap-highlight-color: transparent;
      user-select: none;
      position: relative;
    }
  }
</style>
