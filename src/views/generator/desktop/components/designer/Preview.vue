<template>
  <DesktopLayout>
    <template #head>
      <DesignHead :title="t('设计预览')">
        <template #buttons>
          <div class="design-button">
            <a-button type="primary" class="clean-icon" @click="$emit('close')">{{
              t('关闭')
            }}</a-button>
          </div></template
        >
      </DesignHead>
    </template>
    <div class="content-box" v-if="list.length > 0 && data.show" :key="data.renderKey">
      <grid-layout
        :layout="list"
        :col-num="12"
        :row-height="30"
        :is-draggable="false"
        :is-resizable="false"
        :is-mirrored="false"
        :vertical-compact="false"
        :margin="[10, 10]"
        :use-css-transforms="false"
      >
        <grid-item
          v-for="(item, index) in list"
          :key="index"
          :x="item.x"
          :y="item.y"
          :w="item.w"
          :h="item.h"
          :i="item.i"
          :maxH="item.maxH"
          :minH="item.minH"
          :maxW="item.maxW"
          :minW="item.minW"
        >
          <component
            :id="item.i"
            :w="item.w"
            :h="item.h"
            :type="item.type"
            :config="item.config"
            :title="item.config.title"
            :echarts="item.config.echarts ? item.config.echarts : null"
            class="item-box"
            :is="getLegendComponent(item.type)"
          />
        </grid-item>
      </grid-layout>
    </div>
  </DesktopLayout>
</template>

<script setup lang="ts">
  import { onMounted, onUnmounted, reactive } from 'vue';
  import type { Component } from 'vue';
  import DesignHead from './layout/Head.vue';
  import DesktopLayout from './layout/Desktop.vue';
  import Chart from './Chart.vue';
  import Default from './infos/Default.vue';
  import Dashboard from './infos/Dashboard.vue';
  import Information from './infos/Information.vue';
  import TodoList from './infos/TodoList.vue';
  import Modules from './infos/Modules.vue';
  import { DesktopInfoItem } from '/@/model/desktop/designer';
  import { DesktopComponent } from '/@/enums/desktop';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  defineEmits(['close']);
  withDefaults(
    defineProps<{
      list: Array<DesktopInfoItem>;
    }>(),
    {
      list: () => {
        return [];
      },
    },
  );
  const componentByType = new Map<DesktopComponent, Component>([
    [DesktopComponent.DASHBOARD, Dashboard],
    [DesktopComponent.INFORMATION, Information],
    [DesktopComponent.CHARTLINE, Chart],
    [DesktopComponent.PIE, Chart],
    [DesktopComponent.RADAR, Chart],
    [DesktopComponent.GAUGE, Chart],
    [DesktopComponent.FUNNEL, Chart],
    [DesktopComponent.CHARTBAR, Chart],
    [DesktopComponent.MYTASK, Chart],
    [DesktopComponent.TODOLIST, TodoList],
    [DesktopComponent.MODULES, Modules],
    [DesktopComponent.DEFAULT, Default],
  ]);

  const data: {
    show: boolean;
    renderKey: number;
  } = reactive({
    renderKey: 0,
    show: false,
  });

  onMounted(() => {
    window.addEventListener('resize', () => {
      resetDisplay();
    });
    data.show = true;
  });
  onUnmounted(() => {
    window.removeEventListener('resize', () => {
      resetDisplay();
    });
  });

  function resetDisplay() {
    data.renderKey++;
  }
  function getLegendComponent(type: DesktopComponent) {
    return componentByType.has(type)
      ? componentByType.get(type)
      : componentByType.get(DesktopComponent.DEFAULT);
  }
</script>

<style lang="less" scoped>
  .content-box {
    position: relative;
    width: 100%;
    height: 100%;
    padding-bottom: 60px;
    overflow: auto;
  }

  .vue-grid-layout {
    width: 100%;
    height: 100%;
  }

  .item-box {
    position: relative;
    background-color: #fff;
    width: 100%;
    height: 100%;
    min-width: 10px;
    min-height: 10px;
    box-shadow: 0 3px 6px 1px rgba(0, 0, 0, 0.16);
    border-radius: 4px;
  }

  .vue-grid-item:hover {
    border: 1px dotted #39f;
  }

  .empty-box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .empty {
      width: 480px;
    }
  }
</style>
