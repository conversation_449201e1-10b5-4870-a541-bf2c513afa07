<template>
  <div id="headOffice">
    <div class="comPage_Box">
      <a-tabs v-model:activeKey="activeKey" @change="onChange">
      </a-tabs>
      <div class="filterForm_box">
        <a-cascader
          style="width: 300px"
          v-model:value="searchForm.departList"
          :field-names="{ label: 'name', value: 'id', children: 'children' }"
          :options="cascaderOptions"
          change-on-select
          placeholder="请选择组织架构"
          @change="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.edpCodeOrName"
          placeholder="员工姓名/工号"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-range-picker
          v-model:value="searchForm.time"
          :placeholder="['开始时间', '结束时间']"
          @change="getList()"
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
        />
        <a-button type="primary" @click="getList()">搜索</a-button>
        <a-button @click="reSet()">重置</a-button>
        <a-button class="right_btn" type="primary" @click="onSignSet">导出</a-button>
      </div>
      <div class="table_box">
        <c-table
          :tableColumns="rankColumns"
          :tableData="tableData.data"
          :loading="loading"
          :currentPage="pagination.currentPage"
          :totalItems="pagination.totalItems"
          :pageSize="pagination.pageSize"
          @update:current-page="(value) => (pagination.currentPage = value)"
          @pagination-change="handlePaginationChange"
          rowKey="id"
          :border="true"
          :scroll="{ x: 'auto'}"
        >
        </c-table>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import cTable from '/@/views/components/Table/index.vue';
import {onMounted, reactive, ref} from 'vue';
import { getDepartmentEnabledTree } from "/@/api/system/department";
import {useUserStore} from "/@/store/modules/user";
import {
  exportPerformance,
  exportVisitData,
  getPerformanceList
} from "/@/api/performanceManage/targetManage";
import dayjs from 'dayjs';
import { downloadByData } from "/@/utils/file/download";

const cascaderOptions = ref();
const getDepartMent = async () => {
  try {
    let res = await getDepartmentEnabledTree();
    cascaderOptions.value = res ?? [];
  } catch (err) {
    console.log(err);
  }
};
const searchForm = reactive({
  departList:[],
  edpCodeOrName: '',
  time: null,
  startTime:'',
  endTime:'',
});
const tableData = reactive({
  data: [],
});
const loading = ref(false);
const pagination = reactive({
  currentPage: 1,
  totalItems: 500,
  pageSize: 10,
});
const rankColumns = ref([])

onMounted(async () => {
  getDepartMent()
  // searchForm.time = [
  //   dayjs().format('YYYY-MM-DD'),
  //   dayjs().format('YYYY-MM-DD'),
  // ]
  // searchForm.startTime = dayjs().format('YYYY-MM-DD');
  // searchForm.endTime = dayjs().format('YYYY-MM-DD');
  getList();
  intData();
})

const intData = () => {

}
const tableColumns = [
  {
    title: '员工姓名',
    dataIndex: 'salesmanName',
    key:'salesmanName',
    fixed: 'left',
    width: 120,
    align: 'center',
  },
  {
    title: 'EDP工号',
    dataIndex: 'edpNumber',
    key:'edpNumber',
    fixed: 'left',
    width: 120,
    align: 'center',
  },
  {
    title: '所属辖区',
    dataIndex: 'departNames',
    key:'departNames',
    fixed: 'left',
    width: 280,
    align: 'center',
  },
  {
    title: '门店拜访',
    children: [
      {
        title: '拜访总家数',
        dataIndex: 'visitTotalCount',
        key:'visitTotalCount',
        align: 'center',
        width: 120,
      },
      {
        title: '权重',
        dataIndex: 'visitWeight',
        key:'visitWeight',
        align: 'center',
      },
      {
        title: 'VIP门店家数',
        dataIndex: 'vipStoreFamilyCount',
        key: 'vipStoreFamilyCount',
        align: 'center',
        width: 120,
      },
      {
        title: '权重',
        dataIndex: 'vipStoreWeight',
        key:'vipStoreWeight',
        align: 'center',
      },
      {
        title: '重点门店家数',
        dataIndex: 'keyStoreFamilyCount',
        key: 'keyStoreFamilyCount',
        align: 'center',
        width: 120
      },
      {
        title: '权重',
        dataIndex: 'keyStoreWeight',
        key:'keyStoreWeight',
        align: 'center',
      },
    ]
  },
  {
    title: '贴柜培训',
    children: [
      {
        title: '总场次',
        dataIndex: 'totalSessions',
        key: 'totalSessions',
        align: 'center',
      },
      {
        title: '权重',
        dataIndex: 'totalSessionWeight',
        key: 'totalSessionWeight',
        align: 'center',
      },
      {
        title: '省核心品种场次',
        dataIndex: 'provinceCoreSessions',
        key: 'provinceCoreSessions',
        align: 'center',
        width: 120
      },
      {
        title: '权重',
        dataIndex: 'provinceCoreWeight',
        key: 'provinceCoreWeight',
        align: 'center',
      },
      {
        title: 'VIP门店场次合格家数',
        dataIndex: 'vipStoreQualifiedCount',
        key: 'vipStoreQualifiedCount',
        align: 'center',
        width: 150
      },
      {
        title: '权重',
        dataIndex: 'vipStoreQualifiedWeight',
        key: 'vipStoreQualifiedWeight',
        align: 'center',
      },
      {
        title: '重点门店场次合格家数',
        dataIndex: 'keyStoreQualifiedCount',
        key: 'keyStoreQualifiedCount',
        align: 'center',
        width: 160
      },
      {
        title: '权重',
        dataIndex: 'keyStoreQualifiedWeight',
        key: 'keyStoreQualifiedWeight',
        align: 'center',
      },
    ]
  },
  {
    title: '库存管理',
    children: [
      {
        title: '合格家数',
        dataIndex: 'inventoryQualifiedCount',
        key: 'inventoryQualifiedCount',
        align: 'center',
        width: 100
      },
      {
        title: '权重',
        dataIndex: 'inventoryWeight',
        key: 'inventoryWeight',
        align: 'center',
      },
    ]
  },
  {
    title: '陈列打造',
    children: [
      {
        title: '合格家数',
        dataIndex: 'displayQualifiedCount',
        key: 'displayQualifiedCount',
        align: 'center',
        width: 100
      },
      {
        title: '权重',
        dataIndex: 'displayWeight',
        key: 'displayWeight',
        align: 'center',
      },
    ]
  },
  {
    title: '客情维护',
    children: [
      {
        title: '合格家数',
        dataIndex: 'customerMaintenanceQualifiedCount',
        key: 'customerMaintenanceQualifiedCount',
        align: 'center',
        width: 120
      },
      {
        title: '权重',
        dataIndex: 'customerMaintenanceWeight',
        key: 'customerMaintenanceWeight',
        align: 'center',
      },
    ]
  },
  {
    title: '驻店促销',
    children: [
      {
        title: '驻店总场次',
        dataIndex: 'totalPromotions',
        key: 'totalPromotions',
        align: 'center',
        width: 120,
      },
      {
        title: '权重',
        dataIndex: 'totalPromotionWeight',
        key: 'totalPromotionWeight',
        align: 'center',
      },
      {
        title: 'VIP门店驻店次数',
        dataIndex: 'vipStoreVisits',
        key: 'vipStoreVisits',
        align: 'center',
        width: 130
      },
      {
        title: '权重',
        dataIndex: 'vipStoreVisitWeight',
        key: 'vipStoreVisitWeight',
        align: 'center',
      },
      {
        title: '驻店小票总额',
        dataIndex: 'receiptAmount',
        key: 'receiptAmount',
        align: 'center',
        width: 120
      },
      {
        title: '权重',
        dataIndex: 'receiptAmountWeight',
        key: 'receiptAmountWeight',
        align: 'center',
      },
    ]
  },
  {
    title: '案例收集',
    children: [
      {
        title: '个数',
        dataIndex: 'caseCount',
        key: 'caseCount',
        align: 'center',
      },
      {
        title: '权重',
        dataIndex: 'caseWeight',
        key: 'caseWeight',
        align: 'center',
      },
    ]
  },
  {
    title: '总分',
    children: [
      {
        title: ' ',
        dataIndex: 'totalScore',
        key: 'totalScore',
        align: 'center',
      },
    ]
  },
];
// 获取表格数据
const getList = async (flag?: number) => {
  const userStore = useUserStore();
  const userId = userStore.getUserInfo.id;

  if (!flag) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
  }
  loading.value = true;
  tableData.data = [];
  try {
    let temp = {
      ...searchForm,
      userId,
      performanceDate: searchForm.time?.[0]
        ? dayjs(searchForm.time?.[0]).format('YYYY-MM-DD')
        : '2025-07-20',
      deptId:searchForm.departList?.[searchForm.departList.length - 1] ?? '',
      departList: null,
      edpCodeOrName: searchForm.edpCodeOrName,
      limit: pagination.currentPage,
      size: pagination.pageSize,
      frequency:"1",
    };
    // let temp = {
    //   ...searchForm,
    //   userId,
    //   startTime: searchForm.time?.[0]
    //     ? dayjs(searchForm.time?.[0]).format('YYYY-MM-DD')
    //     : '',
    //   endTime: searchForm.time?.[1]
    //     ? dayjs(searchForm.time?.[1]).format('YYYY-MM-DD')
    //     : '',
    //   deptId:searchForm.departList?.[searchForm.departList.length - 1] ?? '',
    //   departList: null,
    //   edpCodeOrName: searchForm.edpCodeOrName,
    //   limit: pagination.currentPage,
    //   size: pagination.pageSize,
    // };
    let res = await getPerformanceList(temp);
    const processedRankData = res.list.map((item: any, index: number) => ({
      ...item,
      id: item.edpCode || `rank_${index}`, // 使用EDP工号作为ID，如果没有则使用索引
    }));

    tableData.data = processedRankData;
    rankColumns.value = generateRankColumns(processedRankData);
    console.log();
    // tableData.data = res?.list ?? [];
    pagination.totalItems = res.total ?? 0;
    loading.value = false;
  } catch (error) {
    console.log(error);
    loading.value = false;
  }
};
// 处理分页
const handlePaginationChange = (page: any) => {
  pagination.currentPage = page.current;
  pagination.pageSize = page.pageSize;
  getList(1);
};

// 生成动态列配置
const generateRankColumns = (data: any[]) => {
  // 固定列
  const fixedColumns = [
    {
      title: '员工姓名',
      dataIndex: 'saleManName',
      key: 'saleManName',
      align: 'center',
      width: 100,
      minWidth: 100,
    },
    {
      title: 'EDP工号',
      dataIndex: 'edpCode',
      key: 'edpCode',
      align: 'center',
      width: 100,
      minWidth: 100,
    },
    {
      title: '所属辖区',
      dataIndex: 'departNames',
      key: 'departNames',
      align: 'center',
      width: 180,
      minWidth: 180,
    },
  ];

  // 动态列 - 根据taskKpiList生成
  const dynamicColumns: any[] = [];

  if (data && data.length > 0 && data[0].taskKpiList) {
    // 遍历taskKpiList，每个task创建一个合并列
    data[0].taskKpiList.forEach((task: any) => {
      const { taskName, kpiList } = task;

      if (kpiList && kpiList.length > 0) {
        // 为每个task的kpiList创建子列
        const children = kpiList.map((kpi: any) => ({
          title: kpi.kpiName, // 小表头
          dataIndex: `kpi_${taskName}_${kpi.kpiName}`,
          key: `kpi_${taskName}_${kpi.kpiName}`,
          align: 'center',
          width: 100,
          minWidth: 100,
          customRender: ({ record }: any) => {
            // 在record的taskKpiList中找到对应的task，然后在其kpiList中找到对应的kpi
            const taskData = record.taskKpiList?.find((item: any) => item.taskName === taskName);
            if (taskData && taskData.kpiList) {
              const kpiData = taskData.kpiList.find((item: any) => item.kpiName === kpi.kpiName);
              return kpiData ? kpiData.real : '-'; // 绑定real字段
            }
            return '-';
          },
        }));

        // 创建父列（合并列）- taskName作为大表头
        dynamicColumns.push({
          title: taskName, // 大表头
          key: taskName,
          align: 'center',
          children: children,
        });
      }
    });
  }

  return [...fixedColumns, ...dynamicColumns];
};

const reSet = () => {
  searchForm.departList = [];
  searchForm.edpCodeOrName = '';
  searchForm.time = [
    dayjs().format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD'),
  ]
  searchForm.startTime = dayjs().format('YYYY-MM-DD');
  searchForm.endTime = dayjs().format('YYYY-MM-DD');
  getList();
};

const onSignSet = async () => {
  let res: any = null
  let time: any = { startTime: null, endTime: null };
  if (searchForm.time?.length) {
    time = {
      startTime: dayjs(searchForm.time?.[0]).format('YYYY-MM-DD 00:00:00'),
      endTime: dayjs(searchForm.time?.[1]).endOf('month').format('YYYY-MM-DD 23:59:59'),
    };
  }
  let temp = {
    ...searchForm,
    ...time,
    deptId: searchForm.departList?.[searchForm.departList.length - 1] ?? undefined,
  };
  res = await exportPerformance(temp);
  const name ='业绩明细' + dayjs(new Date()).format('YYYY-MM-DD')
   downloadByData(
    res.data,
    `${name}.xlsx`,
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  );
};

</script>
<style scoped lang="less">
#headOffice {
  width: 100%;
  height: 100%;
  padding: 8px;

  .comPage_Box {
    width: 100%;
    height: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;

    > div {
      width: 100%;
      padding: 16px;

      &:last-child {
        flex: 1;
        height: 0;
      }
    }

    .detail_title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 0;
      padding: 16px 0 0 16px;
    }
  }

  .filterForm_box {
    > * + * {
      margin-left: 16px;
    }

    .synchroData_btn {
      float: right;
    }
  }

  .table_box {
  }
}

.modal_box {
  padding: 16px;

  .p_box {
    span {
      margin: 0 4px;
    }

    .ant-select {
      margin-left: 4px;
    }
  }
}
</style>
