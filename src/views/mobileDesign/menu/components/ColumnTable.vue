<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate"> {{ t('新增表格') }} </a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'clarity:note-edit-line',
              title: t('编辑'),
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ant-design:delete-outlined',
              color: 'error',
              title: t('删除'),
              popConfirm: {
                title: t('是否确认删除'),
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
        />
      </template>
    </BasicTable>

    <MenuColumnModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  import { defineComponent, reactive } from 'vue';

  import { BasicTable, useTable, TableAction, BasicColumn } from '/@/components/Table';
  import MenuColumnModal from './MenuColumnModal.vue';

  import { getAppMenuColumnById } from '/@/api/system/menu';

  import { useModal } from '/@/components/Modal';
  import { MenuButtonModel } from '/@/api/system/menuButton/model';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = {
    menuId: { type: String, default: '' },
  };
  export const columns: BasicColumn[] = [
    {
      title: t('表格列名称'),
      dataIndex: 'name',
      width: 100,
      align: 'left',
    },
    {
      title: t('表格列编码'),
      dataIndex: 'code',
      width: 100,
    },
  ];

  export default defineComponent({
    name: 'MenuDrawer',
    components: { TableAction, BasicTable, MenuColumnModal },
    props,
    emits: ['success', 'register'],
    setup(props) {
      let btnData = reactive<MenuButtonModel[]>([]);

      const [registerTable, { getDataSource, setTableData, updateTableDataRecord }] = useTable({
        title: t('表格字段列表'),
        dataSource: btnData,
        api: getAppMenuColumnById,
        beforeFetch: (params) => {
          //发送请求默认新增  左边树结构所选机构id
          return { ...params, menuId: props.menuId };
        },
        columns,
        pagination: false,
        striped: false,
        useSearchForm: false,
        showTableSetting: true,
        bordered: true,
        showIndexColumn: false,
        canResize: false,
        actionColumn: {
          width: 80,
          title: t('操作'),
          dataIndex: 'action',
          slots: { customRender: 'action' },
          fixed: undefined,
        },
      });

      const [registerModal, { openModal }] = useModal();

      function handleCreate() {
        openModal(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        openModal(true, {
          record,
          isUpdate: true,
        });
      }

      function handleSuccess({ isUpdate, record }) {
        if (isUpdate) {
          updateTableDataRecord(record.key, record);
        } else {
          const dataSource = getDataSource();
          dataSource.push(record);
          setTableData(dataSource);
        }
      }

      function handleDelete(record: Recordable) {
        const dataSource = getDataSource();
        setTableData(dataSource.filter((x) => x.key !== record.key));
      }

      return {
        registerTable,
        handleCreate,
        registerModal,
        handleEdit,
        handleSuccess,
        handleDelete,
        getDataSource,
        t,
      };
    },
  });
</script>
