<template>
  <div class="box" v-if="data.show">
    <div class="item-title">
      <span>{{ props.title }}</span>
      <router-link :to="config.path" class="more">{{ t('更多') }}</router-link>
    </div>
    <EmptyBox v-if="data.list.length == 0" />
    <div class="list-box" v-else>
      <div class="row" v-for="(item, index) in data.list" :key="index" @click="selectRow(item)">
        <div>
          <div class="item text-[#666]">{{ item.startUserName }} - {{ item.taskName }}</div>
          <div class="item text-xs">{{ item.startTime }}</div>
        </div>
        <Icon icon="ep:arrow-right" />
      </div>
    </div>
    <ApprovalProcess
      v-if="data.showApproval"
      :taskId="data.taskId"
      :processId="data.processId"
      :schemaId="data.schemaId"
      @close="modelClose"
      :visible="data.showApproval"
    />
  </div>
</template>

<script setup lang="ts">
  import { todoListProperties } from '../config/properties';
  import { getTasks } from '/@/api/workflow/process';
  import { DesktopComponent } from '/@/enums/desktop';
  import { TaskTypeUrl } from '/@/enums/workflowEnum';
  import { EmptyBox } from '/@/components/ModalPanel/index';
  import { TodoListConfig } from '/@/model/mobileDesign/designer';
  import ApprovalProcess from '/@/views/workflow/task/components/ApprovalProcess.vue';
  import { reactive, onMounted, watch } from 'vue';
  import { TasksModel } from '/@/api/workflow/model';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { Icon } from '/@/components/Icon';
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      type: DesktopComponent;
      title: string;
      config: TodoListConfig;
    }>(),
    {
      type: DesktopComponent.DEFAULT,
      config: () => {
        return todoListProperties;
      },
    },
  );
  const data: {
    show: boolean;
    showApproval: boolean;
    taskId: string;
    processId: string;
    schemaId: string;
    list: Array<TasksModel>;
  } = reactive({
    show: false,
    showApproval: false,
    taskId: '',
    processId: '',
    schemaId: '',
    list: [],
  });
  watch(
    () => props.config.renderKey,
    (val) => {
      val && resizeLayout();
    },
    {
      deep: true,
    },
  );
  async function resizeLayout() {
    await getList();
  }
  onMounted(async () => {
    await getList();
    data.show = true;
  });
  async function getList() {
    const searchParams = {
      ...{
        limit: 1,
        size: props.config.maxRows,
      },
    };
    const res = await getTasks(TaskTypeUrl.PENDING_TASKS, searchParams);
    data.list = res.list;
  }
  function selectRow(row: TasksModel) {
    data.taskId = row.taskId;
    data.processId = row.processId;
    data.schemaId = row.schemaId;
    data.showApproval = true;
  }
  function modelClose() {
    data.showApproval = false;
  }
</script>

<style lang="less" scoped>
  .box {
    padding-top: 40px;

    .item-title {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 40px;
      color: #666;
      font-size: 14px;
      padding: 0 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 700;

      span {
        line-height: 18px;
        padding-left: 6px;
        border-left: 6px solid #5e95ff;
      }

      .more {
        color: #5e95ff;
        font-size: 12px;
        font-weight: 400;
      }
    }

    .list-box {
      .row {
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        color: #999;
        font-size: 14px;
        padding: 8px 8px 4px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .item {
          padding: 0 10px 4px;
        }
      }
    }
  }
</style>
