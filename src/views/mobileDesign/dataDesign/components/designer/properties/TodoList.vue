<template>
  <Box v-if="data.show" style="margin-top: 60px">
    <a-form-item :label="t('标题')" :colon="false" labelAlign="right">
      <a-input v-model:value="data.info.config.title" />
    </a-form-item>
    <a-form-item :label="t('最大行数')" :colon="false" labelAlign="right">
      <a-input-number
        v-model:value="data.info.config.maxRows"
        :min="0"
        :max="100"
        @change="resetDisplay"
      />
    </a-form-item>
  </Box>
</template>

<script setup lang="ts">
  import { onMounted, reactive, watch } from 'vue';
  import Box from './Box.vue';
  import { todoListInfo } from '../config/info';
  import { TodoListInfo } from '/@/model/mobileDesign/designer';

  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      info: TodoListInfo;
    }>(),
    {
      info: () => {
        return todoListInfo;
      },
    },
  );
  watch(
    () => props.info,
    (val) => {
      if (val) data.info = val;
    },
    {
      deep: true,
    },
  );
  const data: {
    show: boolean;
    info: TodoListInfo;
  } = reactive({
    show: true,
    info: todoListInfo,
  });

  onMounted(() => {
    data.info = props.info;
    data.show = true;
  });
  function resetDisplay() {
    if (data.info.config.renderKey >= 0) {
      data.info.config.renderKey++;
    }
  }
</script>

<style lang="less" scoped></style>
