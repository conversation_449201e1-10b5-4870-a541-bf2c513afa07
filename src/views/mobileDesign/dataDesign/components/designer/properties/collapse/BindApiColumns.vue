<template>
  <a-select v-model:value="data.value" style="width: 100%" allowClear @change="changeValue">
    <a-select-option v-for="(item, index) in props.apiColumns" :key="index" :value="item.prop">{{
      item.label
    }}</a-select-option>
  </a-select>
</template>

<script setup lang="ts">
  import { reactive, watch } from 'vue';
  const props = withDefaults(
    defineProps<{
      value: string | null;
      apiColumns: Array<{ prop: string; label: string }>;
    }>(),
    {
      value: '',
    },
  );
  watch(
    () => props.value,
    (val) => {
      if (val) data.value = val;
    },
    {
      deep: true,
    },
  );
  const emit = defineEmits(['update:value', 'change']);
  const data = reactive({
    value: props.value,
  });
  function changeValue() {
    emit('update:value', data.value);
    emit('change');
  }
</script>

<style scoped></style>
