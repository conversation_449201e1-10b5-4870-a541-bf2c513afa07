<template>
  <!-- 指标 -->

  <div class="card-box" v-if="data.indicator && data.indicator.length > 0">
    <div class="card-item" v-for="(item, index) in data.indicator" :key="index">
      <a-form :label-col="labelCol">
        <a-form-item :label="t('名称')" :colon="false" labelAlign="left">
          <a-input v-model:value="item.name" @change="changeIndicator" />
        </a-form-item>
        <!-- <a-form-item label="数值" :colon="false" labelAlign="left">
              <a-input-number v-model:value="item.max" />
            </a-form-item> -->
        <a-form-item :label="t('字段')" :colon="false" labelAlign="left">
          <a-select
            v-model:value="item.value"
            style="width: 100%"
            allowClear
            @change="changeIndicator"
          >
            <a-select-option
              v-for="(val, valIndex) in props.apiColumns"
              :key="valIndex"
              :value="val.prop"
              >{{ val.label }}</a-select-option
            >
          </a-select>
        </a-form-item>
      </a-form>
      <div class="close-icon" v-if="data.indicator && data.indicator.length > 1">
        <Icon
          icon="ant-design:close-circle-outlined"
          color="#ff8080"
          :size="20"
          @click="deleteItem(index)"
        />
      </div>
    </div>
  </div>
  <a-button type="primary" @click="add">{{ t('添加') }}</a-button>
</template>

<script setup lang="ts">
  import { reactive } from 'vue';
  import Icon from '/@/components/Icon/index';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const labelCol = { style: { width: '40px' } };
  const props = withDefaults(
    defineProps<{
      indicator: Array<{ name: string; value: string }>;
      apiColumns: Array<{ prop: string; label: string }>;
    }>(),
    {
      indicator: () => {
        return [];
      },
    },
  );

  const emit = defineEmits(['update:indicator', 'change']);
  const data = reactive({
    indicator: props.indicator,
  });
  function changeIndicator() {
    emit('update:indicator', data.indicator);
    emit('change');
  }
  function add() {
    let name = t('指标') + Number(data.indicator.length + 1);
    data.indicator.push({
      value: '',
      name,
    });
    changeIndicator();
  }
  function deleteItem(index: number) {
    data.indicator.splice(index, 1);
    changeIndicator();
  }
</script>

<style lang="less" scoped>
  .card-box {
    .card-item {
      position: relative;
      margin: 10px 0;
      padding: 8px;
      background: #f5f5f5;
      border-radius: 4px;
    }

    .close-icon {
      position: absolute;
      top: -10px;
      right: -6px;
    }
  }
</style>
