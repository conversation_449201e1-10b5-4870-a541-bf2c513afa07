import { Component, defineAsyncComponent } from 'vue';
import { DesktopComponent } from '/@/enums/desktop';
export default function () {
  const Chart = defineAsyncComponent({
    loader: () => import('./../components/designer/chart/Chart.vue'),
  });
  const ChartBar = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/ChartBar.vue'),
  });
  const MixLineBar = defineAsyncComponent({
    loader: () => import('./../components/designer/chart/MixLineBar.vue'),
  });
  const Banner = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Banner.vue'),
  });
  const Dashboard = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Dashboard.vue'),
  });
  const MyTask = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/MyTask.vue'),
  });
  const TodoList = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/TodoList.vue'),
  });
  const Modules = defineAsyncComponent({
    loader: () => import('./../components/designer/infos/Modules.vue'),
  });
  const DefaultProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Default.vue'),
  });
  const ChartlineProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Chartline.vue'),
  });
  const ChartBarProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/ChartBar.vue'),
  });
  const GaugeProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Gauge.vue'),
  });
  const FunnelProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Funnel.vue'),
  });
  const RadarProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Radar.vue'),
  });
  const DashboardProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Dashboard.vue'),
  });
  const PieProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Pie.vue'),
  });
  const TodoListProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/TodoList.vue'),
  });
  const ModulesProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Modules.vue'),
  });
  const BannerProperties = defineAsyncComponent({
    loader: () => import('./../components/designer/properties/Banner.vue'),
  });
  const componentByType = new Map<DesktopComponent, { legend: Component; properties: Component }>([
    [DesktopComponent.DASHBOARD, { legend: Dashboard, properties: DashboardProperties }],
    [DesktopComponent.BANNER, { legend: Banner, properties: BannerProperties }],
    [DesktopComponent.CHARTLINE, { legend: MixLineBar, properties: ChartlineProperties }],
    [DesktopComponent.PIE, { legend: Chart, properties: PieProperties }],
    [DesktopComponent.RADAR, { legend: Chart, properties: RadarProperties }],
    [DesktopComponent.GAUGE, { legend: Chart, properties: GaugeProperties }],
    [DesktopComponent.FUNNEL, { legend: Chart, properties: FunnelProperties }],
    [DesktopComponent.CHARTBAR, { legend: ChartBar, properties: ChartBarProperties }],
    [DesktopComponent.MYTASK, { legend: MyTask, properties: DefaultProperties }],
    [DesktopComponent.TODOLIST, { legend: TodoList, properties: TodoListProperties }],
    [DesktopComponent.MODULES, { legend: Modules, properties: ModulesProperties }],
  ]);
  return {
    componentByType,
  };
}
