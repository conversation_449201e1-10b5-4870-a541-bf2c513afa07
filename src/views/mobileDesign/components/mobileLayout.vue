<template>
  <!-- 中间区域 -->

  <div class="design-bg-box">
    <div class="head-box">
      <div class="head-toolbar">
        <div class="w-12 font-bold">9:00</div>
        <div class="head-metba">
          <div class="dot"></div>
          <div class="head-line"></div>
          <div class="dot"></div>
        </div>
        <div>
          <Icon icon="ph:wifi-high-fill" />
          <Icon icon="game-icons:network-bars" />
          <Icon icon="mdi:battery" />
        </div>
      </div>
      <div class="text-center font-bold">页面标题</div>
    </div>
    <div class="design-box">
      <div class="design-content-box">
        <slot name="designContent"></slot>
      </div>
    </div>
    <div class="foot-line"></div>
  </div>
</template>

<script setup lang="ts">
  import { Icon } from '/@/components/Icon';
</script>

<style lang="less" scoped>
  .design-bg-box {
    margin: 70px auto;
    width: 399px;
    border: 6px solid rgb(205 208 220);
    border-radius: 40px;
    box-shadow: 1px 5px 12px 8px #f2f2f2;
    height: calc(100vh - 200px);
    padding: 12px;
    position: relative;

    .head-box {
      position: absolute;
      left: 13px;
      right: 13px;
      top: 13px;
      background: #fff;
      padding: 7px 10px 10px;
      border-top-right-radius: 26px;
      border-top-left-radius: 26px;
      border-bottom: 2px solid rgb(245 245 245);

      .head-toolbar {
        display: flex;
        justify-content: space-between;
      }

      .head-metba {
        border: 1px solid #666;
        width: 120px;
        height: 26px;
        margin-top: -8px;
        border-top-color: #fff;
        border-bottom-left-radius: 16px;
        border-bottom-right-radius: 16px;
        display: flex;
        justify-content: space-around;
        padding-top: 6px;

        .dot {
          width: 10px;
          background: #ccc;
          height: 10px;
          border-radius: 50%;
        }

        .head-line {
          width: 40px;
          background: #ccc;
          height: 8px;
          border-radius: 4px;
        }
      }
    }

    .design-box {
      border: 1px solid #666;
      border-radius: 26px;
      height: calc(100% - 40px);
      background-color: rgb(245 245 245);
      padding-top: 72px;
      padding-bottom: 10px;

      .design-content-box {
        overflow-y: auto;
        overflow-x: hidden;
        height: 100%;
        margin: 0 -80px;
        padding: 0 90px;

        &::-webkit-scrollbar {
          display: none;
          width: 0;
          height: 0;
          opacity: 0;
        }
      }
    }

    .foot-line {
      width: 70px;
      background: #ccc;
      height: 8px;
      border-radius: 4px;
      margin: 20px auto;
    }
  }
</style>
