<template>
  <div class="form-config-container">
    <a-form v-bind="layout">
      <a-form-item :label="t('组件尺寸')">
        <a-radio-group v-model:value="data.size" button-style="solid" size="small">
          <a-radio-button value="default">{{ t('默认') }}</a-radio-button>
          <a-radio-button value="large">{{ t('大') }}</a-radio-button>
          <a-radio-button value="small">{{ t('小') }}</a-radio-button>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts">
  import { WidgetForm } from '/@/components/Designer/src/types';
  import { defineComponent, PropType, ref, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  export default defineComponent({
    name: 'AntdFormConfig',
    props: {
      config: {
        type: Object as PropType<WidgetForm['config']>,
        required: true,
      },
    },
    emits: ['update:config'],
    setup(props, context) {
      const data = ref(props.config);
      const layout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
      };
      watch(data, () => context.emit('update:config', data));

      return {
        data,
        layout,
        t,
      };
    },
  });
</script>
<style scoped lang="less">
  .form-config-container {
    padding: 10px;
  }
</style>
