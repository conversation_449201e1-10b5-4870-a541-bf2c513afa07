//工作流  流程设计 节点
export enum BpmnNodeKey {
  START = 'bpmn:StartEvent', //开始节点
  END = 'bpmn:EndEvent', //结束节点
  INCLUSIVE = 'bpmn:InclusiveGateway', //相容网关
  PARALLEL = 'bpmn:ParallelGateway', //并行网关
  EXCLUSIVE = 'bpmn:ExclusiveGateway', //互斥网关
  USER = 'bpmn:UserTask', //用户任务
  SCRIPT = 'bpmn:ScriptTask', //脚本任务
  SCRIPT_CONTENT = 'bpmn:script', //脚本任务 脚本内容
  SUB_PROCESS = 'bpmn:SubProcess', //子流程
  PROCESS = 'bpmn:Process', //流程
  SEQUENCEFLOW = 'bpmn:SequenceFlow', //流程线
  CALLACTIVITY = 'bpmn:CallActivity', //外部流程
  EVENT = 'bpmn:Event',
  COUNTERSIGN = 'bpmn:MultiInstanceLoopCharacteristics', //会签节点  外部节点多实例
  COUNTERSIGN_CONDITION = 'bpmn:completionCondition', //会签条件
  CONDITION_EXPRESSION = 'bpmn:FormalExpression', //流程线条件
}
// 表单信息
export enum FormType {
  SYSTEM = 0, //系统表单
  CUSTOM, //自定义表单
  WORKFLOW, //流程已添加表单
}
//人员权限类型
export enum MemberType {
  USER = 0, //0 用户
  ROLE, //1 角色
  POST, //2 岗位
  SPECIFY_NODE_APPROVER, //3 指定节点审批人
  SUPERIOR_LEADERS, //4 上级领导
  FORM_FIELD, //5 表单字段
  API, //6 api调用
  CURRENT_NODE_CANDIDATE, //7 当前节点候选审批人
  FORM_CREATOR, //8 表单创建人
  APPROCAL_USER, // 9 审批专员
}
// 自动同意
export enum AutoAgreeRule {
  NO_RULE = 0, //未选择自动同意规则
  ORIGINATOR, //候选审批人包含流程任务发起人
  PREVIOUS_NODE, //候选审批人包含上一节点审批人
  APPROVED, // 候选审批人在此流程中审批过
}
// 无处理人
export enum NoHandler {
  ADMIN = 0, //由超级管理员处理
  PREVIOUS_NODE, //由上一节点审批人指定审批人
}
// 指定审批人
export enum DesignatedApprover {
  NOT_SPECIFIED = 0, //不指定审批人
  PREVIOUS_NODE, //由上一节点审批人指定
}

//权限类型
export enum AuthType {
  ALL = 0, // 0 所有
  APPOINT, // 1 指定
}
// 任务状态
export enum ProcessStatus {
  APPROVAL_INPROGRESS = 0, //审批中
  APPROVAL_COMPLETED, //审批通过
}
// 任务权限
export enum TaskPermissions {
  LIMITED_TO_PROMOTER_INITIATED = 0, //限发起人发起
  EVERYONE_LAUNCHES, //所有人发起
}
//参数类型
export enum ParamType {
  VALUE = 0, //值
  VARIABLE, //变量
  API, //api
  FORM_DATA, //表单数据
}

//脚本记录
export enum RecordType {
  NO_RECORD = 0, //不在流程记录中记录脚本任务操作信息
  RECORD, //记录脚本任务操作信息
}

//通知消息类型
export enum NoticePolicyType {
  SYSTEM_MESSAGES = 0, // 系统消息
  SHORT_MESSAGE, // 短信
  ENTERPRISE_WECHAT, // 企业微信
  DING_TALK, // 钉钉
  MAILBOX, //邮箱
}
//会签类型
export enum MultipleInstancesType {
  NONE = 0, //无
  SYNC, //同步
  ASYNC, //异步
}

export enum AddOrRemoveType {
  ALLOW = 1, //允许
  FORBID = 0, //禁止
}

//会签 完成条件(多实例)
export enum InstanceCompletionConditions {
  ALL = 0, //全部
  SINGLE, //单个
  PERCENTAGE, //百分比
}

//审批意见展示类型
export enum ApprovalOpinionDisplayType {
  ALL = 0, //显示所有
  SHOW_FINAL_RESULTS, //显示最终结果
}
//电子签章验证
export enum ElectronicSignatureVerification {
  NO_PASSWORD_REQUIRED = 0, //直接使用不需要密码
  PASSWORD_REQUIRED, //填写密码
}

export enum LevelEnum {
  ONE = 1, //上1级领导
  SECOND, //上2级领导
  THREE, //上3级领导
  FOUR, //上4级领导
  FIVE, //上5级领导
}
// 参数类型
export enum OperationType {
  VALUE = 0, //值
  VARIABLE, //变量
  API, //APi
}

// 按钮类型
export enum ButtonType {
  DEFAULT = 0,
  SCRIPT = 1,
  API = 2,
}
// 按钮 驳回类型
export enum RejectType {
  ALL = 0, // 允许驳回至任一流转过的节点
  ONLY = 1, // 仅允许驳回至上一节点
}

// 外部流程 调用类型
export enum CallActivityType {
  SINGLE = 0, // 单实例
  MULTIPLE = 1, // 多实例
}
// 外部流程  完成条件
export enum FinishType {
  ALL = 0, //全部
  SINGLE, //单个
  PERCENTAGE, //百分比
}

// 外部流程 执行类型
export enum ExecutionType {
  SEQUENCE = 0, //顺序执行
  PARALLEL, //并行执行
}

// 流程任务类型
export enum TaskTypeUrl {
  PENDING_TASKS = '/workflow/execute/pending', //待办
  FINISHED_TASKS = '/workflow/execute/finished/page', //已办
  MY_PROCESS = '/workflow/execute/my-process/page', //我的流程
  CIRCULATED = '/workflow/execute/circulated/page', //我的传阅
  RECYCLE = '/workflow/execute/my-process/recycle/page', //回收站
  DRAFT = '/workflow/execute/draft/page', //草稿箱
}
// 工作流分类id
export enum FlowCategory {
  ID = '1419276800524425555',
}

//  审批类型 0 同意 1 拒绝 2 驳回 3 结束 4 其他（用户自定义按钮）5 暂存
export enum ApproveType {
  AGREE = 0, //同意
  DISAGREE, //拒绝
  REJECT, //驳回
  FINISH, //结束
  OTHER, //其他（用户自定义按钮）
  STAGING, //暂存
}
export enum ApproveCode {
  AGREE = 'agree', //同意
  DISAGREE = 'disagree', //拒绝
  REJECT = 'reject', //驳回
  FINISH = 'finish', //结束
  OTHER = 'other', //其他（用户自定义按钮）
  STAGING = 'staging', //暂存
}
// 流程监控状态
export enum ProcessMonitorStatus {
  SUSPENDED = 'SUSPENDED', //挂起
  ACTIVE = 'ACTIVE', //活动
  COMPLETED = 'COMPLETED',
  INTERNALLY_TERMINATED = 'INTERNALLY_TERMINATED',
}

// 签章类型

export enum StampType {
  PRIVATE_SIGNATURE = 0, //私人签章
  PUBLIC_SIGNATURE, //公共签章
  DEFAULT_SIGNATURE, //默认签章
}
// 签章文件类型
export enum StampFileTypeAttributes {
  UPLOAD_PICTURES = 0, //上传照片
  HANDWRITTEN_SIGNATURE, //手写签名
}

// 签章分类id
export enum StampCategory {
  ID = '1585911685466951681',
}

//事件类型
export enum NodeEventType {
  START = 0, //开始事件
  END, //结束事件
}

//事件执行类型
export enum NodeEventExType {
  API = 0, //api
  LITEFLOW, //规则引擎
  PUSH_MESSAGE, //推送消息
}

//用户节点超时处理
export enum TimeOutHandle {
  NO = 1, //不启用
  YES = 2, //启用
}
//用户节点超时机制
export enum TimeOutRule {
  MAX = 1, //超过最大推送次数则即时处理
  FIRST = 2, //首次超时即处理
}
//用户节点处理方式
export enum TimeOutType {
  AGREE = 2, //自动同意并向下流转
  BACK = 1, //自动驳回至上一节点
}
// 表单form页面来源
export enum FromPageType {
  MENU = 0, //菜单页面
  FLOW, //工作流
  PREVIEW, //预览
  DESKTOP, //桌面设计
}
