import { TreeConfigColumn } from './treeStructure';
import { AppDataType } from '/@/enums/appEnum';
import { FormSettingItem } from '/@/model/workflow/formSetting';

/**
 * 列表配置
 */
export interface ListConfig {
  isAdvancedQuery: boolean; //是否配置高级搜索
  querySelectOption: string; //所有查询配置
  objectId: string; //系统表单formId,自定义表单releaseId的id值
  //是否有左侧菜单
  isLeftMenu: boolean;
  //是否分页
  isPage: boolean;
  //分页 每页显示条数
  pageSize?: number;

  //左侧菜单配置
  leftMenuConfig?: LeftMenuConfig;
  //查询配置
  queryConfigs: QueryConfig[];
  //列表配置
  columnConfigs: ColumnConfig[];
  //按钮配置
  buttonConfigs: ButtonConfig[];
  // //合计配置
  // totalConfigs?: TotalConfig[];
  //列表标题
  listTitle?: string;
  //是否默认排序
  defaultOrder?: boolean;
  //排序字段
  orderBy?: string;
  //排序类型 (默认 倒叙)
  orderType?: 'desc' | 'asc';
  //列表样式
  listStyle?: string;
  //排列方式
  arrangeType?: string;
}

/**
 * 左侧菜单配置
 */
export interface LeftMenuConfig {
  //左侧菜单是否树结构
  // isTree: boolean;
  //左侧菜单宽度 分为 按照 1/3 | 1/4 | 1/5
  leftWidth?: 1 | 2 | 3;
  //如果不是数据字典 就是数据源
  datasourceType: string; //dic | datasource | api
  //如果是静态数据 则需要静态数据列表
  staticData?: Array<any>;
  //如果是数据源 就需要有数据源id
  datasourceId?: string;
  //如果是数据源 则需要配置 数据字段名 （与parentFiledName 搭配拼接树结构）
  fieldName?: string;
  //如果是数据源 则需要配置 父级字段名 （与fieldName 搭配拼接树结构）
  parentFiledName?: string;
  // 显示字段
  showFieldName?: string;
  //如果是数据源 需要 关联字段 关联 列表关联字段
  relationFieldName?: string;
  //列表关联字段
  listFieldName?: string;
  //数据项Id 用户查询数据字典详情
  dictionaryItemId?: string;
  //如果是api 则需要api配置
  apiConfig?: any;
  //菜单显示名称
  menuName?: string;
  //父级菜单图标
  parentIcon?: string;
  //子级菜单图标
  childIcon?: string;
  treeConfig: TreeConfigColumn;
}

/**
 * 查询配置
 */
export interface QueryConfig {
  //查询字段
  fieldName: string;
  //查询框所占宽度
  width?: number;
  //是否时间字段
  isDate: boolean;
  //时间选择和日期选择需要
  format?: string;
}

/**
 * 列表配置
 */
export interface ColumnConfig {
  //组件唯一标识
  key?: string;
  //显示名
  label?: string;
  //列名
  columnName: string;
  //对齐
  alignType?: string;
  //宽度
  columnWidth?: string;
  //自适应
  autoWidth?: boolean;
  //是否合计
  isTotal?: boolean;
  //是否是数字类型
  isNumber?: boolean;
  //组件类型
  componentType?: string;
  //时间选择、日期选择、时间范围、日期范围需要
  format?: string;
  //组件配置
  componentProps?: any;
  //手机端是否显示标签
  showLabel?: boolean;
  //手机端是否主字段，当主字段启用时，该字段的标签和内容的字体大小增加2px，然后字体加粗
  mainField?: boolean;
  //是否列头筛选
  isFilter?: boolean;
  //是否独占一行
  aRow?: boolean;
  //字段名加粗
  textBold?: boolean;
}

/**
 * 按钮配置
 */
export interface ButtonConfig {
  //是否启用
  isUse: boolean;
  //按钮名
  name: string;
  //编码
  code: string;
  //图标
  icon: string;
  //是否新增
  isDefault: boolean;
  //配置信息
  setting?: any;
}

/**
 * 合计配置
 */
export interface TotalConfig {
  //需要合计字段
  fieldName: string;
}

export interface AppDataItem {
  formType?: AppDataType; //数据类型
  code: string; //数据ID
  name: string; //数据名称
  img?: string;
}

/**
 * 推单配置
 */
export interface PushOrderConfig {
  type: string; //类型
  formInfo: Array<FormSettingItem>; //表单配置
  apiConfig: any; //表单配置
}
