import { FormJson } from './codeGenerator';
import { ColumnConfig, ListConfig } from './listConfig';
import { MenuConfig } from './menuConfig';
import { OutputConfig } from './outputConfig';
import { TableConfig } from './tableConfig';
import { TableStructureConfig } from './tableStructureConfig';
import { FormEventColumnConfig } from './formEventConfig';

import { FormTypeEnum } from '/@/enums/formtypeEnum';

/**
 * 表单设计器 代码优先/界面优先
 */
export interface GeneratorConfig {
  //数据库id
  databaseId: string | null;
  //数据表配置
  tableConfigs?: TableConfig[];
  //表单配置
  formJson: FormJson;
  //列表页面配置
  listConfig: ListConfig;
  //输出配置
  outputConfig: OutputConfig;
  //菜单配置
  menuConfig?: MenuConfig;
  //表结构配置
  tableStructureConfigs?: TableStructureConfig[];
  //表单事件配置
  formEventConfig: FormEventColumnConfig;
  //是否开启数据权限（自定义表单）
  isDataAuth?: boolean;
  //数据权限选择（自定义表单）
  dataAuthList?: string[] | number[];
  //表单分类id
  categoryId?: string;
  //代码模板名称
  name?: string;
  formId?: string;
  //是否开启审计字段
  isCommonFields?: boolean;
  //是否转为数据优先
  toDataFirst?: boolean;
}

/**
 * 自定义表单 配置项
 */
export interface CustomFormConfig {
  name: string;
  category: string; //分类
  formDesignType: number; // 0-数据优先 1-界面优先 2-简易模板
  formType: FormTypeEnum; //表单类型
  remark: string;
  formJson: CustomFormJson;
  isChange?: boolean; //是否是编辑状态
}

export interface CustomFormJson {
  //数据库id
  databaseId: string;
  //数据表配置
  tableConfigs?: TableConfig[];
  //表单配置
  formJson: FormJson;
  //表结构配置
  tableStructureConfigs?: TableStructureConfig[];
  //表单事件配置
  formEventConfig?: FormEventColumnConfig;
  //是否开启数据权限
  isDataAuth: boolean;
  //数据权限选择
  dataAuthList: string[] | number[];
  //是否开启审计字段
  isCommonFields?: boolean;
}

/**
 * 表单发布
 */
export interface FormReleaseConfig {
  //表单id
  formId: string;
  //列表配置
  listConfig?: ListConfig;
  //菜单配置
  menuConfig: MenuConfig;
}

/**
 * 调查问卷
 */
export interface QuestionnaireConfig {
  id?: string;
  className: string;
  //数据库id
  databaseId: string | null;
  //表单配置
  formJson: FormJson;
  //列表配置
  columnConfigs: ColumnConfig[];
  outputArea: string;
  //菜单配置
  menuConfig: MenuConfig;
  //表结构配置
  tableStructureConfigs: TableStructureConfig[];
  //分类id
  category: string;
  //名称
  name?: string;
  //是否开启审计字段
  isCommonFields?: boolean;
  comment: string;
  remark: string;
}
