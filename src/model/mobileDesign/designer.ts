import { EChartsOption } from 'echarts';
import { AlignType, DesktopComponent, LegendType, OrientType } from '/@/enums/desktop';
import { ApiConfig } from '/@/components/ApiConfig/src/interface';

export type DesktopInfoId = string;
export type InfoType = DesktopComponent;

export interface MobileData extends MobileBasicData {
  jsonContent: string; ////移动数据设计json
  pageCode?: string; ////移动数据设计前端代码
}
export interface MobileBasicData {
  id?: string;
  code: string; //编码
  name: string; //名称
  icon?: string; //图标
  sortCode?: number; //排序
  categoryId?: string | null; //功能类别
  isMenu: number; //菜单
  remark: string; //描述
  enabledMark?: number; //状态 1:启用，0：禁用，-1：草稿
  appMenuId?: string; //菜单ID
}
export interface DesktopConfig {
  title: string;
  renderKey: number;
  echarts?: undefined | EChartsOption;
}

export interface BasicInfo {
  h?: number | undefined;
  i: string;
  minH?: number | undefined;
  type: DesktopComponent;
}
export interface ApiInfo {
  apiData: { [x: string]: number };
  apiColumns: any;
  apiConfig: ApiConfig;
}
export interface DesktopInfoItem extends BasicInfo {
  config: DesktopConfig | MixLineBarConfig;
}

// 饼图 config
export interface PieConfig extends ApiInfo {
  title: string; //标题
  renderKey: number;
  labelKey: string; //维度
  valueKey: string; //指标
  colors: Array<string>; //颜色配置
  autoWidth?: boolean; //宽度自动
  echarts: EChartsOption;
}
// 柱状图 config
export interface ChartBarConfig extends ApiInfo {
  title: string; //标题
  renderKey: number;
  labelKey: string; //维度
  valueKey: string; //指标
  targetKey: string; //目标值
  unit: string;
}
export interface ColumnItem {
  id: string;
  name: string;
  width: number;
  align: AlignType;
}
export interface InformationConfig extends ApiInfo {
  title: string; //标题
  renderKey: number;
  jumpId: string | undefined;
  path: string | undefined;
  maxRows: number;
  columns: Array<ColumnItem>;
}

export interface DashboardConfig extends ApiInfo {
  renderKey: number;
  dashboard: Array<dashboardItem>;
  numColor: string | undefined;
  labelColor: string | undefined;
}

export interface BannerConfig {
  renderKey: number;
  imgs: Array<bannerItem>;
}
export interface DefaultInfo extends BasicInfo {
  config: DesktopConfig;
}
export interface ChartsInfo extends BasicInfo {
  config: DesktopConfig;
}
export interface DashboardInfo extends BasicInfo {
  config: DesktopConfig & DashboardConfig;
}

export interface BannerInfo extends BasicInfo {
  config: DesktopConfig & BannerConfig;
}
export interface InformationInfo extends BasicInfo {
  config: DesktopConfig & InformationConfig;
}

// 饼图配置
export interface PieItemInfo extends BasicInfo {
  config: DesktopConfig & PieConfig;
}
// 柱状图
export interface ChartBarInfo extends BasicInfo {
  config: DesktopConfig & ChartBarConfig;
}
export interface ChartLineItemInfo extends BasicInfo {
  config: {
    title: string;
    echarts: EChartsOption;
  };
}
export interface functionItem {
  id: string;
  name: string;
  icon: string;
  url: string;
  color: string;
}
export interface dashboardItem {
  name: string;
  field: string | null;
}
export interface bannerItem {
  index: number;
  url: string | null;
}
export interface ModulesConfig {
  functions: Array<functionItem>;
  isShowTitle: boolean;
}
export interface ModulesInfo extends BasicInfo {
  config: DesktopConfig & ModulesConfig;
}
export interface TodoListConfig {
  title: string;
  renderKey: number;
  maxRows: number;
  path: {};
}
export interface TodoListInfo extends BasicInfo {
  config: DesktopConfig & TodoListConfig;
}

export interface ChartXAxis {
  position: string;
  name: string;
  nameLocation: string;
  nameTextStyle: {
    color: string;
    fontSize: number;
    fontWeight: string;
  };
  type: string;
  axisLabel: {
    formatter: string;
    color: string;
  };
  axisLine: {
    show: boolean;
    lineStyle: {
      color: string;
    };
  };
  data: Array<any>;
}
export interface ChartYAxis {
  name: string;
  nameLocation: string;
  nameTextStyle: {
    color: string;
    fontSize: number;
    fontWeight: string;
  };
  min?: string;
  max?: string;
  interval: null;
  position: string;
  type: string;
  axisLabel: {
    formatter: string;
    color: string;
  };
  axisLine: {
    show: boolean;
    lineStyle: {
      color: string;
    };
  };
}

export interface ChartLabel {
  show: boolean;
  color: string;
  position?: string;
  fontWeight?: string;
  fontSize: number;
  formatter: string | null;
}
export interface ChartApiColumn {
  prop: string;
  label: string;
}
export interface ChartLegend {
  show: true;
  orient: OrientType;
  left: string;
  top: string;
  right: string;
  bottom: string;
  padding: number;
  icon: LegendType;
  itemWidth: number;
  itemHeight: number;
  textStyle: {
    color: string;
  };
  formatter: string;
  width?: string | number;
  position: number;
}

// 柱状图折线图混合
export interface MixLineIndicator {
  //title: string;
  type: string;
  color: string;
  name: string;
  value: string;
  showAreaStyle: boolean; //是否显示面积图
  gradualStartColor: string;
  gradualEndColor: string;
}
export interface MixLineBarDataItem {
  title: string;
  valueKey: string;
  total: number;
  apiData: Array<any>;
  apiColumns: Array<ChartApiColumn>;
  apiConfig: ApiConfig;
  indicator: Array<MixLineIndicator>;
}
export interface EchartsSeriesItem {
  name: string;
  type: string;
  smooth: boolean;
  areaStyle: any;
  stack: string;
  label: {
    show: boolean;
    color: string;
  };
  data: Array<any>;
  showAreaStyle: boolean;
  gradualEndColor: string;
  gradualStartColor: string;
  symbol?: string;
  yAxisIndex?: number;
}

export interface IndicatorItem {
  title: string;
  type: string;
  color: string;
  name: string;
  value: string;
}
export interface MixLineBarDataListItem {
  title: string;
  valueKey: string;
  apiData: Object;
  indicator: Array<IndicatorItem>;
  apiConfig: ApiConfig;
}
export interface EchartsItem {
  color: Array<string>;
  legend: {
    data: Array<string>;
  };
  xAxis: Array<{
    type: string;
    data: Array<any>;
  }>;
  yAxis: Array<{
    type: string;
  }>;
  series: Array<EchartsSeriesItem>;
}
export interface MixLineBarConfig {
  title: string;
  renderKey: number;
  condition: {
    color: string;
    selected: string;
  };
  legend: ChartLegend;
  isCrosswise?: boolean; //是否横向
  // 统计
  count: {
    show: boolean;
    unit: string;
    title: string;
  };
  dataList: Array<MixLineBarDataItem>;
  bar: {
    stack: boolean; //是否显示堆叠
    label: {
      show: boolean;
      color: string;
    };
  };
  line: {
    smooth: boolean;
    stack: boolean; //是否显示堆叠
    showAreaStyle: boolean; //是否显示面积图
    gradualStartColor: string;
    gradualEndColor: string;
    showSymbol: boolean; //是否显示标记
    label?: ChartLabel;
  };
  label: ChartLabel;
  yAxis: Array<ChartYAxis>;
  xAxis: Array<ChartXAxis>;
  echarts: Array<EchartsItem>;
}
export interface MixLineBarInfo extends BasicInfo {
  config: MixLineBarConfig;
}
