import { FormType } from '/@/enums/workflowEnum';

export interface TableItem {
  defaultValue?: any;
  required: boolean;
  view: boolean;
  edit: boolean;
  disabled: boolean;
  isSaveTable?: boolean;
  tableName: string;
  fieldName: string;
  fieldId: string;
  key: string;
  isSubTable: boolean;
  showChildren: boolean;
  type: string;
  options?: any;
  children: Array<TableItem>;
}
export interface FormSettingItem {
  key: string; //formId_key
  formType: FormType; //表单类型
  formId: string; //表单ID   系统表单为文件夹名
  formName: string; //表单名称
  name?: string; //表单原始名称
  enabledMark?: number; //禁用:0 禁用 1 启用
  functionName?: string; //模块名称
  functionalModule?: string; //模块地址
  config?: any; //配置信息
}
export interface FormConfigItem {
  type: string;
  key: string; //formId_key
  name?: string; //表单原始名称
  formType: FormType; //表单类型
  formId: string; //表单ID   系统表单为文件夹名
  formName: string; //表单名称
  showChildren: boolean;
  requiredAll: boolean;
  viewAll: boolean;
  editAll: boolean;
  children: Array<TableItem>;
}
// 表单设置
export interface FormSetting {
  formConfigs: Array<FormSettingItem>;
}
// 功能表单
export interface formInitConfig {
  enabled: boolean;
  formType: FormType; //表单类型
  formId: string; //表单ID   系统表单为文件夹名
  formName: string; //表单名称
}
// 更新表单 表单数据
export interface UpdateFormConfigItem {
  formKey: string;
  formName: string;
  formId: string;
  formType: FormType;
  nodeName: string;
  status: boolean;
  remark: string;
}
