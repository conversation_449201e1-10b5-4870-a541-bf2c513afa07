import { MemberConfig } from './memberSetting';
import { FormConfigItem, FormSettingItem } from './formSetting';
import {
  EndEventConfig,
  GatewayConfig,
  ProcessConfig,
  ScriptTaskConfig,
  StartEventConfig,
  UserTaskConfig,
  OpinionConfig,
  SequenceFlowConfig,
  CallActivityConfig,
} from './workflowConfig';
import { ApproveType, BpmnNodeKey, FormType } from '/@/enums/workflowEnum';
import { ButtonConfigItem } from '/@/model/workflow/workflowConfig';
interface basicInfoItem {
  id: string;
  name: string;
  type: BpmnNodeKey;
  remark: string;
}
export type InfoId = string;
export type InfoItem = basicInfoItem &
  StartEventConfig &
  UserTaskConfig &
  ScriptTaskConfig &
  GatewayConfig &
  SequenceFlowConfig &
  CallActivityConfig &
  EndEventConfig;
export type InfoType = BpmnNodeKey;

export interface ProcessJson {
  resourceId: string; //流程id 唯一id
  processConfig: ProcessConfig; //流程属性配置
  childNodeConfig: basicInfoItem[]; //子节点配置
}
export interface BpmnState {
  modelId: string;
  info: Map<InfoId, InfoItem>;
  infoId: InfoId;
  processInfo: ProcessConfig;
  defaultFormList: Array<FormSettingItem>;
}

export interface FormInfoItem {
  formData: Record<string, any> | undefined;
  formConfig: FormConfigItem;
  formJson: string;
  formType: FormType;
}
export interface FlowInfo {
  isCountersign: boolean;
  isAddOrSubSign: boolean;
  schemaInfo: {
    category: string;
    xmlContent: string;
    name: string;
    id: string;
    code: string;
    remark: string;
  };
  formInfos: Array<FormInfoItem>;
  taskRecords: Array<TaskRecord>;
  otherProcessApproveRecord: Array<TaskRecordList>;
  taskApproveOpinions: Array<TaskApproveOpinion>;
  relationTasks: Array<any>;
  buttonConfigs?: Array<ButtonConfigItem>;
  opinionConfig?: OpinionConfig;
  taskApproveOpinionListMap?: any;
  formAssignmentData: null | undefined | Recordable; //参数赋值
  isStaging?: boolean; //是否是暂存
  approveDto?: PostApprovalData; //暂存信息
}
export interface BpmnFlowFormItem {
  id: string;
  name: string;
  code: string;
  categoryName: string;
  remark: string;
}
export interface TaskRecord {
  comment: string;
  nodeName: string;
  nodeType: string;
  startTime: string;
  circulateMessage: string;
}
export interface TaskRecordList {
  records: Array<TaskRecord>;
  schemaName: string;
}
export interface TaskApproveOpinion {
  approveComment: string;
  id: string;
  approveStampUrl: string;
  approveTime: string;
  approveUserName: string;
  approveType: ApproveType;
  approveResult: string;
}
export interface BpmnFlowForm {
  // schemaId: string;
  item: BpmnFlowFormItem;
  xml: string;
  formInfos: Array<FormInfoItem>;
  relationTasks: Array<{ schemaId: string; schemaName: string }>;
  taskRecords: Array<TaskRecordList>;
  taskApproveOpinions: any;
  predecessorTasks: Array<SchemaTaskItem>;
  opinions: Array<TaskApproveOpinion>;
  opinionsComponents: Array<string>;
  hasStamp: Boolean;
  hasStampPassword: Boolean;
  submitLoading: Boolean;
  formAssignmentData: null | Recordable; //参数赋值
}
// 工作流表单校验
export type ValidateForms = Array<ValidateFlowForm>;
export interface ValidateFlowForm {
  validate: boolean;
  msgs: Array<ValidateFlowFormErrorMsg>;
  result?: any;
  formKey?: string;
  isOldSystem?: boolean;
}
export interface ValidateFlowFormErrorMsg {
  errors: Array<string>;
  name: Array<string>;
}
// 前置任务 列表

export interface RelationTaskPage {
  pageSize: number;
  total: number;
  list: Array<PredecessorTaskItem>;
}
export interface PredecessorTaskItem {
  createTime: string;
  currentSchedule: null;
  originator: string;
  schemaName: string;
  taskId: string;
  taskName: string;
  processId: string;
}
export interface TaskItem {
  taskId: string;
  taskName: string;
  processId: string;
}
// 任务数据
export interface SchemaTaskItem {
  schemaId: string;
  schemaName: string;
  taskId: string;
  taskName: string;
  processId: string;
}

// 提交流程后返回数据格式
export type SubmittingProcessData = Array<{
  provisionalApprover?: boolean;
  isAppoint: boolean;
  isMultiInstance: boolean;
  taskId: string;
  taskName: string;
  selectIds: Array<string>;
}>;
// 下一节点审批人
export interface ApproveTask {
  taskId: string;
  taskName: string;
  provisionalApprover?: boolean;
  selectIds: Array<string>;
}

export interface BatchApproverUser {
  taskId: string;
  approvedUsers: Array<string>;
}

export interface BatchApproverInfo {
  needStamp: boolean;
  needPassword?: boolean;
}
export type BatchApproverUsersParams = Array<BatchApproverUser>;

// 审批数据
export interface PostApprovalData {
  approvedType: number;
  approvedContent: string;
  formData: Recordable;
  rejectNodeActivityId: string;
  taskId: string;
  circulateConfigs: Array<MemberConfig>;
  stampId: string;
  stampPassword: string;
  approvedResult: string;
  fileFolderIds: Array<string>;
  isOldSystem: Object;
}
// 批量审批
export interface PostBatchApprovalData {
  approvedType: number;
  approvedContent: string;
  taskIds: Array<string>;
  stampId: string;
  stampPassword?: string;
}

// 批量审批
export interface GetBatchApprovalInfo {
  taskIds: string;
}

//驳回节点信息
export interface rejectNodeItem {
  activityId: string;
  activityName: string;
}

// 工作里调用系统表单
export interface WorkFlowFormParams {
  formConfigChildren: Array<FormConfigItem>; // 工作流节点表单权限配置
  formConfigKey: String; //唯一节点表单key
  opinions?: Array<TaskApproveOpinion> | undefined; //意见簿意见
  opinionsComponents?: Array<string> | undefined; //意见簿组件
  isViewProcess: boolean;
  uploadIds: Array<string>;
  formModels: Recordable;
  formId?: String;
  formName?: String;
}
// 系统表单参数
export interface SystemComponentConfig {
  functionalModule: string; //功能模块
  functionName: string; //功能名称
  functionFormName: string; //Form页面名称
}
//催办参数
export interface UrgeParams {
  processId: string;
  taskId: string;
  urgeType?: string;
}
